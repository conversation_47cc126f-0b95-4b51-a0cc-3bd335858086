<?php

use App\Enums\Email\EmailBladeFiles;
use App\Models\Email\Email;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Email::whereIn('blade_file_name', [EmailBladeFiles::RegisteredEmailDirect])->update([
            'display_name' => 'Registered Email Direct',
        ]);
    }
};
