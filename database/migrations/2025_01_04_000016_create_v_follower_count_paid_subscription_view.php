<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("create view v_follower_count_paid_subscription(name, label, follower_count) as
                            SELECT c.name, p.label, count(f.id) AS follower_count
                            FROM companies AS c
                                     JOIN company_profile_types AS p ON c.company_profile_types_id = p.id
                                     JOIN users_following_companies AS f ON c.id = f.followed_company_id
                            WHERE c.company_profile_types_id IN (SELECT id
                                                                 FROM company_profile_types
                                                                 WHERE value IN
                                                                       ('VENDOR_BASIC':::STRING, 'VENDOR_PLUS':::STRING, 'VENDOR_PREMIUM':::STRING,
                                                                        'VENDOR_ENTERPRISE':::STRING))
                            GROUP BY c.name, p.label
                            ORDER BY c.name;");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW v_follower_count_paid_subscription');
    }
};
