<?php

use App\Models\Company\CompanyType;
use App\Models\Permission\Feature\CompanyTypePermissionFeatureGroupFeature;
use App\Models\Permission\Role\TemplatePermissionRole;
use App\Models\Permission\Role\TemplateRole;
use App\Models\Permission\Role\TemplateRoleCompanyType;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    private array $templatePermissionsRoles = ['SUPER_ADMIN_DIRECT', 'READ_ONLY_DIRECT', 'MANAGER_CONTRACT_DIRECT',
        'MANAGER_INTEGRATION_DIRECT', 'MANAGER_FINANCE_DIRECT'];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $companyType = CompanyType::select('id')->where('value', 'DIRECT')->first();
        if ($companyType) {
            $date = now();
            $templateRoles = $this->prepareDirectRoles($date);
            TemplateRole::insert($templateRoles);
            $this->prepareTemplatePermissionsRoles($date, $companyType->id);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        TemplateRole::whereIn('key', $this->templatePermissionsRoles)->delete();
    }

    private function prepareDirectRoles($date): array
    {
        $templateRoles[] = [
            'title' => 'Direct Super Admin',
            'display_name' => 'Super Admin',
            'key' => 'SUPER_ADMIN_DIRECT',
            'description' => 'Full permissions across the profile, zero limitations.',
            'created_at' => $date,
            'updated_at' => $date,
        ];
        $templateRoles[] = [
            'title' => 'Direct Finance Manager',
            'display_name' => 'Finance Manager',
            'key' => 'MANAGER_FINANCE_DIRECT',
            'description' => 'Full permissions to the finance.',
            'created_at' => $date,
            'updated_at' => $date,
        ];
        $templateRoles[] = [
            'title' => 'Direct Contract/Product Manager',
            'display_name' => 'Contract/Product Manager',
            'key' => 'MANAGER_CONTRACT_DIRECT',
            'description' => 'Full permissions to the contracts and products.',
            'created_at' => $date,
            'updated_at' => $date,
        ];
        $templateRoles[] = [
            'title' => 'Direct Integration Manager',
            'display_name' => 'Integration Manager',
            'key' => 'MANAGER_INTEGRATION_DIRECT',
            'description' => 'Full permissions to Integrations.',
            'created_at' => $date,
            'updated_at' => $date,
        ];
        $templateRoles[] = [
            'title' => 'Direct Read-Only',
            'display_name' => 'Read-Only',
            'key' => 'READ_ONLY_DIRECT',
            'description' => 'Able to access and view specific company information, zero edit capabilities.',
            'created_at' => $date,
            'updated_at' => $date,
        ];

        return $templateRoles;
    }

    private function prepareTemplatePermissionsRoles($date, $companyTypeId): void
    {
        $templateRoles = TemplateRole::select('id', 'key')->whereIn('key', array_merge($this->templatePermissionsRoles, ['RESTRICTED_ACCESS']))->get();
        $templateRolesInsert = [];
        foreach ($templateRoles as $templateRole) {
            $templateRolesInsert[] = [
                'template_role_id' => $templateRole->id,
                'company_type_id' => $companyTypeId,
                'created_at' => $date,
                'updated_at' => $date,
            ];
        }
        TemplateRoleCompanyType::insert($templateRolesInsert);
        $permissionGroups = CompanyTypePermissionFeatureGroupFeature::select('permission_feature_id')
            ->with('permissionFeature.permissionGroups:id,permission_feature_id,key')
            ->where('company_type_id', $companyTypeId)
            ->get()
            ->pluck('permissionFeature.permissionGroups')->flatten();
        $templatePermissionsRoles = [];
        foreach ($templateRoles as $templateRole) {
            switch ($templateRole->key) {
                case 'SUPER_ADMIN_DIRECT':
                    foreach ($permissionGroups as $permissionGroup) {
                        $templatePermissionsRoles[] = [
                            'template_role_id' => $templateRole->id,
                            'permission_group_id' => $permissionGroup->id,
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                    break;
                case 'READ_ONLY_DIRECT':
                    $subPermissionGroups = $permissionGroups->whereIn('key',
                        ['COMPANY_PROFILE_READ',
                            'MANAGE_STACK_READ',
                            'MANAGE_CONTRACTS_READ']);
                    foreach ($subPermissionGroups as $permissionGroup) {
                        $templatePermissionsRoles[] = [
                            'template_role_id' => $templateRole->id,
                            'permission_group_id' => $permissionGroup->id,
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                    break;
                case 'MANAGER_CONTRACT_DIRECT':
                    $subPermissionGroups = $permissionGroups->whereIn('key',
                        ['COMPANY_PROFILE_READ',
                            'INTEGRATIONS_READ',
                            'MANAGE_STACK_READ', 'MANAGE_STACK_UPDATE',
                            'PLAID_MGNMT_READ',
                            'MANAGE_CONTRACTS_READ', 'MANAGE_CONTRACTS_UPDATE',
                            'DATA_EXPORT_READ', 'DATA_EXPORT_UPDATE',
                            'MANAGE_REPORTS_READ', 'MANAGE_REPORTS_UPDATE',
                            'MANAGE_STACK_READ', 'MANAGE_STACK_UPDATE']);
                    foreach ($subPermissionGroups as $permissionGroup) {
                        $templatePermissionsRoles[] = [
                            'template_role_id' => $templateRole->id,
                            'permission_group_id' => $permissionGroup->id,
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                    break;
                case 'MANAGER_FINANCE_DIRECT':
                    $subPermissionGroups = $permissionGroups->whereIn('key',
                        ['COMPANY_PROFILE_READ', 'COMPANY_PROFILE_UPDATE',
                            'USER_MANAGEMENT_READ', 'USER_MANAGEMENT_UPDATE',
                            'INTEGRATIONS_READ', 'INTEGRATIONS_UPDATE',
                            'MANAGE_STACK_READ', 'MANAGE_STACK_UPDATE',
                            'PLAID_MGNMT_READ', 'PLAID_MGNMT_UPDATE',
                            'MANAGE_CONTRACTS_READ', 'MANAGE_CONTRACTS_UPDATE',
                            'DATA_EXPORT_READ', 'DATA_EXPORT_UPDATE',
                            'MANAGE_REPORTS_READ', 'MANAGE_REPORTS_UPDATE']);
                    foreach ($subPermissionGroups as $permissionGroup) {
                        $templatePermissionsRoles[] = [
                            'template_role_id' => $templateRole->id,
                            'permission_group_id' => $permissionGroup->id,
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                    break;
                case 'MANAGER_INTEGRATION_DIRECT':
                    $subPermissionGroups = $permissionGroups->whereIn('key',
                        ['COMPANY_PROFILE_READ',
                            'INTEGRATIONS_READ', 'INTEGRATIONS_UPDATE',
                            'MANAGE_STACK_READ',
                            'PLAID_MGNMT_READ',
                            'MANAGE_CONTRACTS_READ', 'MANAGE_CONTRACTS_UPDATE'
                            ]);
                    foreach ($subPermissionGroups as $permissionGroup) {
                        $templatePermissionsRoles[] = [
                            'template_role_id' => $templateRole->id,
                            'permission_group_id' => $permissionGroup->id,
                            'created_at' => $date,
                            'updated_at' => $date,
                        ];
                    }
                    break;
            }
        }
        TemplatePermissionRole::insert($templatePermissionsRoles);
    }
};
