version: 0.2
env:
  secrets-manager:
    BITBUCKET_USERNAME: bitbucket-credentials:username
    BITBUCKET_PASSWORD: bitbucket-credentials:password
    S3AGENT_ACCESS_KEY_ID: s3agent-credentials:S3AGENT_ACCESS_KEY_ID
    S3AGENT_ACCESS_KEY: s3agent-credentials:S3AGENT_ACCESS_KEY
  parameter-store:
    DOCKER_USER: "/cicd/docker-login"
    DOCKER_PASSWORD: "/cicd/docker-password"
    APP_NAME: $(echo $CODEBUILD_SOURCE_VERSION | cut -f2 -d '/')
phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://0.0.0.0:2375 --storage-driver=overlay&
  pre_build:
    commands:
      - DATE=$(date +%Y-%m-%d-%H-%M-%S)
      - aws ecr get-login-password | docker login --username AWS --password-stdin ${REPOSITORY_URI}
      - HASH="$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | head -c 8)"
      - IMAGE_URI="${REPOSITORY_URI}:${HASH}-${DATE}"
      - |
        echo "Sending mail notifying the start of the build."
        date=$(date)
        message='{"Subject":{ "Data": "Deploying '"$ENVIRONMENT"' for backend on '"$date"'" },"Body":{"Html":{"Data": "<body>Deploying '"$ENVIRONMENT"' for backend on '"$date"'<body/>"}}}'
        echo $message > message.txt
        destination='{ "ToAddresses": ["<EMAIL>"] }'
        echo $destination > destination.txt
        aws ses send-email --from <EMAIL> --destination=file://destination.txt --message=file://message.txt
  build:
    commands:
      - |
        REPO_TAG=$(aws ssm get-parameter --name "/DeploymentConfigs/${ENVIRONMENT}/${APP_NAME}/REPO_TAG" --with-decryption --query "Parameter.Value" --output text)
        echo This environment is $ENVIRONMENT, hence cloning using tags.
        echo "REPO_TAG=" $REPO_TAG
        rm -rf .??* *
        git clone --branch $REPO_TAG https://$BITBUCKET_USERNAME:$<EMAIL>/channelprogram/$APP_NAME.git .
      - echo Build started on `date`
      - echo "Creating Environment File"
      - aws s3api get-object --bucket build-helpers --key docker/cve-scan.sh ./cve-scan.sh
      - chmod +x cve-scan.sh
      - echo Building the Docker image...
      - docker login --username ${DOCKER_USER} --password ${DOCKER_PASSWORD}
      - echo $IMAGE_URI
      - rm -rf .env
      - |
        echo "Retrieving BE secrets from AWS Secrets Manager."
        aws secretsmanager get-secret-value --secret-id ${ENVIRONMENT}/be/env_vars  --query SecretString --output text | jq '.' | jq -r "to_entries|map(\"\(.key)=\(.value|tostring)\")|.[]" > .env
        echo "Secrets for BE environments retreived."
      - cat .env | grep -E 'APP_NAME|APP_ENV|APP_DEBUG|APP_URL|FE_URL'
      - docker build --build-arg BASE_TAG=$BASE_IMAGE_TAG --build-arg GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN --tag "$IMAGE_URI" .
      - echo "Enabling Image Scanning for repository ${APP_NAME}-${ENVIRONMENT} "
  post_build:
    commands:
      - |
        if [ $CODEBUILD_BUILD_SUCCEEDING -ne 1 ]; then
          echo "Sending mail notifying the fail of the build."
          date=$(date)
          message='{"Subject":{ "Data": "Build fail on env '"$ENVIRONMENT"' for backend on '"$date"'" },"Body":{"Html":{"Data": "<body>Build fail for env '"$ENVIRONMENT"' for backend on '"$date"'<body/>"}}}'
          echo $message > message.txt
          destination='{ "ToAddresses": ["<EMAIL>"] }'
          echo $destination > destination.txt
          aws ses send-email --from <EMAIL> --destination=file://destination.txt --message=file://message.txt
        fi
      - echo "CODEBUILD_BUILD_SUCCEEDING=" $CODEBUILD_BUILD_SUCCEEDING
      - '[ ${CODEBUILD_BUILD_SUCCEEDING:-0} -eq 1 ] || exit 1'
      - echo "$IMAGE_URI"
      - docker push "$IMAGE_URI"
      - printf '{"Tag":"%s-%s"}' $HASH $DATE > build.json
      - cat build.json
      - echo Build completed on `date`
      - pwd
      - echo $BUCKET_NAME
      - echo $DB_URL
      - DB_BACKUP_SUBPATH=$(basename $SERVICE_NAME "-svc")
      - echo $DB_BACKUP_SUBPATH
      - |
        ECR_IMAGE="$IMAGE_URI"
        echo "The ECR Image is: " $ECR_IMAGE
        TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition ${TASK_DEFINITION_NAME} --region "$AWS_REGION") # Name of task should be there
        echo "Old task definiton is: " $TASK_DEFINITION

        # Updates the current task definition by setting the new image to the previous
        # Task definition
        NEW_TASK_DEFINTIION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$ECR_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
        echo "New task definition is: " $NEW_TASK_DEFINTIION

        # Registers the newly created task definition
        NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINTIION")
        echo "New task info is: " $NEW_TASK_INFO
        NEW_REVISION=$(echo $NEW_TASK_INFO | jq '.taskDefinition.revision')
        echo "Task defintion is: " $TASK_DEFINITION
        echo "New revision is: " $NEW_REVISION
      - |
        curl -X POST -H "Content-Type: application/json" \
          -u $BITBUCKET_USERNAME:$BITBUCKET_PASSWORD  https://bitbucket.org/api/2.0/repositories/channelprogram/channel_program_be/pullrequests \
          -d '{ "title": "Merge latest release branch", "description": "Merge release branch to master", "source": { "branch": { "name": "release" }, "repository": { "full_name": "channelprogram/channel_program_be" } }, "destination": { "branch": { "name": "master" } }, "reviewers": [{"username": "jhonatan-castro"}, {"username": "channelprogram-admin"}], "close_source_branch": false }'

      # Artifact file
      - printf '[{"ecs_cluster_name":"prod-cp","ecs_service_name":"prod-cp-be-svc","task_definition_family":"important-component-prod-cp-be-svc-production-svc"}]' > ecs_data.json
artifacts:
    files:
        - ecs_data.json
