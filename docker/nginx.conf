events {
    worker_connections 1024;
}

http {
    upstream backend {
        least_conn;  # Use least connections algorithm for better load distribution
        server laravel.test:80;
        server laravel.test2:80;
        server laravel.test3:80;
        server laravel.test4:80;
        server laravel.test5:80;
        keepalive 32;  # Keep connections alive
    }

    # Add some basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    # Include MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        
        location / {
            proxy_pass http://backend;
            proxy_http_version 1.1;  # Use HTTP/1.1 for keepalive
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";  # Required for keepalive
        }
    }

    server {
        listen 443 ssl;

        ssl_certificate     /etc/nginx/ssl/channel_program_be.test.crt;
        ssl_certificate_key /etc/nginx/ssl/channel_program_be.test.key;

        location / {
            proxy_pass         http://backend;
            proxy_http_version 1.1;  # Use HTTP/1.1 for keepalive
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
            proxy_set_header   Connection "";  # Required for keepalive
        }
    }
}
