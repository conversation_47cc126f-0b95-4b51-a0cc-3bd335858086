<?php

namespace Tests\Feature\MyStack;

use Tests\TestCase;

class MyStackCustomerControllerTest extends TestCase
{
    private string $showAll = 'api/v1/customer/{company}/my-stack';
    private string $showAllCategoriesFilled = 'api/v1/customer/{company}/my-stack/categories-filled';
    private string $store = 'api/v1/customer/{company}/my-stack/store';
    private string $update = 'api/v1/customer/{company}/my-stack/update';
    private string $delete = 'api/v1/customer/{company}/my-stack/delete';
    private string $updatePartnerStatus = 'api/v1/customer/{company}/my-stack/update/partner-status';
    private string $generateCSV = 'api/v1/customer/{company}/my-stack/generate-csv';
    private string $generatePDF = 'api/v1/customer/{company}/my-stack/generate-pdf';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->showAllCategoriesFilled));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->updatePartnerStatus));
        $this->assertTrue($this->checkRouteExists($this->generateCSV));
        $this->assertTrue($this->checkRouteExists($this->generatePDF));
    }
}
