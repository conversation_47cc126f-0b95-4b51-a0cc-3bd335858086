<?php

namespace Tests\Feature\MyStack;

use Tests\TestCase;

class MyStackCompanyControllerTest extends TestCase
{
    private string $showAll = 'api/v1/company/{company}/my-stack';
    private string $showAllCategoriesFilled = 'api/v1/company/{company}/my-stack/categories-filled';
    private string $missingAdoptionStack = 'api/v1/company/{company}/my-stack/missing-adoption-stack';
    private string $store = 'api/v1/company/{company}/my-stack/store';
    private string $update = 'api/v1/company/{company}/my-stack/update';
    private string $delete = 'api/v1/company/{company}/my-stack/delete';
    private string $deleteCategory = 'api/v1/company/{company}/my-stack/delete-category';
    private string $updatePartnerStatus = 'api/v1/company/{company}/my-stack/update';
    private string $generateCSV = 'api/v1/company/{company}/my-stack/generate-csv';
    private string $downloadPDF = 'api/v1/company/{company}/my-stack/download-pdf';
    private string $showClientUsage = 'api/v1/company/{company}/my-stack/{myStack}/client-usage';
    private string $addClientUsage = 'api/v1/company/{company}/my-stack/{myStack}/client-usage';
    private string $deleteClientUsage = 'api/v1/company/{company}/my-stack/{myStack}/client-usage';
    private string $showAllAffiliate = 'api/v1/company/{company}/affiliate/my-stack';
    private string $showAllAffiliateCategoriesFilled = 'api/v1/company/{company}/affiliate/my-stack/categories-filled';

    public function test_routes(): void
    {
        $this->assertTrue($this->checkRouteExists($this->showAll));
        $this->assertTrue($this->checkRouteExists($this->showAllCategoriesFilled));
        $this->assertTrue($this->checkRouteExists($this->missingAdoptionStack));
        $this->assertTrue($this->checkRouteExists($this->store));
        $this->assertTrue($this->checkRouteExists($this->update));
        $this->assertTrue($this->checkRouteExists($this->delete));
        $this->assertTrue($this->checkRouteExists($this->deleteCategory));
        $this->assertTrue($this->checkRouteExists($this->updatePartnerStatus));
        $this->assertTrue($this->checkRouteExists($this->generateCSV));
        $this->assertTrue($this->checkRouteExists($this->downloadPDF));
        $this->assertTrue($this->checkRouteExists($this->showClientUsage));
        $this->assertTrue($this->checkRouteExists($this->addClientUsage));
        $this->assertTrue($this->checkRouteExists($this->deleteClientUsage));
        $this->assertTrue($this->checkRouteExists($this->showAllAffiliate));
        $this->assertTrue($this->checkRouteExists($this->showAllAffiliateCategoriesFilled));
    }
}
