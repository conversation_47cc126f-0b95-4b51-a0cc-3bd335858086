<?php

use App\Http\Controllers\Api\ActivityLogs\ActivityLogsController;
use App\Http\Controllers\Api\Admin\ActivityLogs\AdminActivityLogsController;
use App\Http\Controllers\Api\Admin\Advertisement\AdminAdvertisementCardTypeController;
use App\Http\Controllers\Api\Admin\Advertisement\AdminAdvertisementController;
use App\Http\Controllers\Api\Admin\AffiliateBrands\AdminAffiliateBrandsController;
use App\Http\Controllers\Api\Admin\Analytics\Event\EventAnalyticsController;
use App\Http\Controllers\Api\Admin\Analytics\Video\ViewCountsController;
use App\Http\Controllers\Api\Admin\Analytics\Video\ViewCountsDetailController;
use App\Http\Controllers\Api\Admin\BroadcastMessages\AdminBroadcastMessagesController;
use App\Http\Controllers\Api\Admin\Category\AdminCategoryController;
use App\Http\Controllers\Api\Admin\ChannelDeals\AdminChannelDealsClaimedController;
use App\Http\Controllers\Api\Admin\ChannelDeals\AdminChannelDealsController;
use App\Http\Controllers\Api\Admin\Chart\Metric\AdminChartMetricController;
use App\Http\Controllers\Api\Admin\Chart\PitchEvent\AdminChartPitchEventController;
use App\Http\Controllers\Api\Admin\Chart\PitchPoll\AdminChartPitchPollController;
use App\Http\Controllers\Api\Admin\Chart\Quadrant\AdminChartQuadrantController;
use App\Http\Controllers\Api\Admin\Chart\Vendor\AdminChartVendorController;
use App\Http\Controllers\Api\Admin\Comment\AdminCommentController;
use App\Http\Controllers\Api\Admin\Company\AdminCompanyClaimerController;
use App\Http\Controllers\Api\Admin\Company\AdminCompanyClaimerResponseController;
use App\Http\Controllers\Api\Admin\Company\AdminCompanyController;
use App\Http\Controllers\Api\Admin\Company\AdminCompanyDetailController;
use App\Http\Controllers\Api\Admin\Company\Locations\AdminCompanyLocationController;
use App\Http\Controllers\Api\Admin\Currency\AdminCurrencyController;
use App\Http\Controllers\Api\Admin\CustomView\CustomViewController;
use App\Http\Controllers\Api\Admin\Email\AdminEmailAllowedParameterController;
use App\Http\Controllers\Api\Admin\Email\AdminEmailController;
use App\Http\Controllers\Api\Admin\Email\AdminEmailTemplateTextController;
use App\Http\Controllers\Api\Admin\Email\EmailWhitelistController;
use App\Http\Controllers\Api\Admin\Focuses\AdminFocusController;
use App\Http\Controllers\Api\Admin\Focuses\AdminFocusOptionController;
use App\Http\Controllers\Api\Admin\IndustryEvent\AdminIndustryEventsController;
use App\Http\Controllers\Api\Admin\JobTitleController\AdminJobTitleController;
use App\Http\Controllers\Api\Admin\Lookup\AdminLookupOptionController;
use App\Http\Controllers\Api\Admin\Lookup\AdminLookupOptionValuesController;
use App\Http\Controllers\Api\Admin\Maint\ArtisanController;
use App\Http\Controllers\Api\Admin\Migration\MigrateContractsController;
use App\Http\Controllers\Api\Admin\Notification\AdminNotificationController;
use App\Http\Controllers\Api\Admin\Permission\AdminPermissionController;
use App\Http\Controllers\Api\Admin\Permission\Feature\AdminCompanyTypePFGFeatureController;
use App\Http\Controllers\Api\Admin\Permission\Feature\AdminPermissionFeatureController;
use App\Http\Controllers\Api\Admin\Permission\Feature\AdminPermissionFeatureGroupController;
use App\Http\Controllers\Api\Admin\Permission\Role\AdminRoleController;
use App\Http\Controllers\Api\Admin\Permission\Role\AdminTemplateRoleController;
use App\Http\Controllers\Api\Admin\PitchEvent\AdminPitchDayController;
use App\Http\Controllers\Api\Admin\PitchEvent\AdminPitchEventController;
use App\Http\Controllers\Api\Admin\PitchEvent\MSPAttendants\AdminPitchEventMSPAttendantsController;
use App\Http\Controllers\Api\Admin\PitchEvent\Question\AdminPitchEventQuestionsController;
use App\Http\Controllers\Api\Admin\ProfanityFilter\ProfanityFilterController;
use App\Http\Controllers\Api\Admin\Profile\AdminProfileController;
use App\Http\Controllers\Api\Admin\Profile\AdminProfileRuleController;
use App\Http\Controllers\Api\Admin\Profile\AdminProfileRuleValueController;
use App\Http\Controllers\Api\Admin\ProfileEnrichment\AdminProfileEnrichmentOptionController;
use App\Http\Controllers\Api\Admin\ProfileEnrichment\AdminProfileEnrichmentQuestionController;
use App\Http\Controllers\Api\Admin\Question\AdminQuestionController;
use App\Http\Controllers\Api\Admin\Question\AdminQuestionFormController;
use App\Http\Controllers\Api\Admin\Question\AdminQuestionFormTypeController;
use App\Http\Controllers\Api\Admin\Question\AdminQuestionOptionController;
use App\Http\Controllers\Api\Admin\Redis\RedisController;
use App\Http\Controllers\Api\Admin\Review\AdminProductReviewController;
use App\Http\Controllers\Api\Admin\Review\AdminReviewController;
use App\Http\Controllers\Api\Admin\Review\AdminReviewIncentiveController;
use App\Http\Controllers\Api\Admin\Review\AdminReviewQuestionController;
use App\Http\Controllers\Api\Admin\Review\AdminReviewQuestionOptionController;
use App\Http\Controllers\Api\Admin\SocialMedia\AdminSocialMediaController;
use App\Http\Controllers\Api\Admin\Tag\AdminTagController;
use App\Http\Controllers\Api\Admin\Tango\TangoApiController;
use App\Http\Controllers\Api\Admin\Tango\TangoCatalogController;
use App\Http\Controllers\Api\Admin\User\AdminUserController;
use App\Http\Controllers\Api\Admin\UserContent\AdminUserContentController;
use App\Http\Controllers\Api\Advertisement\AdvertisementAvailableLocationController;
use App\Http\Controllers\Api\Advertisement\AdvertisementController;
use App\Http\Controllers\Api\Analytics\AnalyticsController;
use App\Http\Controllers\Api\Blog\BlogController;
use App\Http\Controllers\Api\Bookmark\UserProductBookmarkController;
use App\Http\Controllers\Api\Bulletin\BulletinController;
use App\Http\Controllers\Api\Bulletin\BulletinFilterController;
use App\Http\Controllers\Api\Category\CategoryController;
use App\Http\Controllers\Api\ChangePasswordController;
use App\Http\Controllers\Api\ChannelDeals\ChannelDealLogController;
use App\Http\Controllers\Api\ChannelDeals\ChannelDealsClaimedController;
use App\Http\Controllers\Api\ChannelDeals\ChannelDealsController;
use App\Http\Controllers\Api\Chart\PitchPoll\ChartPitchPollController;
use App\Http\Controllers\Api\Chart\ProductReviews\ChartProductReviewsController;
use App\Http\Controllers\Api\Chart\Quadrant\ChartQuadrantController;
use App\Http\Controllers\Api\Chat\OneToOneChatController;
use App\Http\Controllers\Api\ClientProduct\ClientProductController;
use App\Http\Controllers\Api\ClientVendor\ClientVendorController;
use App\Http\Controllers\Api\Comment\CommentController;
use App\Http\Controllers\Api\Company\CompanyAffiliateController;
use App\Http\Controllers\Api\Company\CompanyClaimerController;
use App\Http\Controllers\Api\Company\CompanyClaimerResponseController;
use App\Http\Controllers\Api\Company\CompanyClientController;
use App\Http\Controllers\Api\Company\CompanyConfigurationController;
use App\Http\Controllers\Api\Company\CompanyContactListController;
use App\Http\Controllers\Api\Company\CompanyController;
use App\Http\Controllers\Api\Company\CompanyDashboardController;
use App\Http\Controllers\Api\Company\CompanyHubspotController;
use App\Http\Controllers\Api\Company\CompanyInviteController;
use App\Http\Controllers\Api\Company\CompanyMarketplacePartnerController;
use App\Http\Controllers\Api\Company\CompanyNavigationFavoritesController;
use App\Http\Controllers\Api\Company\CompanyNotificationRecipientsController;
use App\Http\Controllers\Api\Company\CompanySearchContentController;
use App\Http\Controllers\Api\Company\CompanySocialController;
use App\Http\Controllers\Api\Company\CompanyUserController;
use App\Http\Controllers\Api\Company\CompanyWhitelabelingController;
use App\Http\Controllers\Api\CompanySupportSchedule\CompanySupportScheduleController;
use App\Http\Controllers\Api\Configuration\AppConfigurationController;
use App\Http\Controllers\Api\ContactUsController;
use App\Http\Controllers\Api\Contract\ContractController;
use App\Http\Controllers\Api\Contract\ContractDocumentController;
use App\Http\Controllers\Api\Contract\ContractLogController;
use App\Http\Controllers\Api\Contract\ContractNotificationController;
use App\Http\Controllers\Api\ContractAndCustomerStackMigrationController;
use App\Http\Controllers\Api\CountryController;
use App\Http\Controllers\Api\Cpjobs\CpJobsController;
use App\Http\Controllers\Api\Currency\CurrencyController;
use App\Http\Controllers\Api\Cyclr\CyclrController;
use App\Http\Controllers\Api\Deal\DealController;
use App\Http\Controllers\Api\Deal\DealDocumentController;
use App\Http\Controllers\Api\Deal\DealLogController;
use App\Http\Controllers\Api\Email\CustomizableEmailController;
use App\Http\Controllers\Api\EmailBlackListController;
use App\Http\Controllers\Api\EmailChangeController;
use App\Http\Controllers\Api\EnumController;
use App\Http\Controllers\Api\Explorer\ExplorerController;
use App\Http\Controllers\Api\Explorer\ExplorerProfilesController;
use App\Http\Controllers\Api\Explorer\ExplorerVideosPageController;
use App\Http\Controllers\Api\FeatureFlagController;
use App\Http\Controllers\Api\Files\FileUploadController;
use App\Http\Controllers\Api\Filter\FilterChartController;
use App\Http\Controllers\Api\Filter\FilterContractController;
use App\Http\Controllers\Api\Filter\FilterController;
use App\Http\Controllers\Api\Filter\FilterRecurrenceController;
use App\Http\Controllers\Api\Focus\FocusController;
use App\Http\Controllers\Api\Folder\FolderContentController;
use App\Http\Controllers\Api\Folder\FolderController;
use App\Http\Controllers\Api\Follow\FollowCompanyController;
use App\Http\Controllers\Api\Follow\FollowUserController;
use App\Http\Controllers\Api\Google\GoogleApiController;
use App\Http\Controllers\Api\ImageController;
use App\Http\Controllers\Api\IndustryEvent\IndustryEventsController;
use App\Http\Controllers\Api\Integration\IntegrationController;
use App\Http\Controllers\Api\JobTitle\JobTitleController;
use App\Http\Controllers\Api\LanguageController;
use App\Http\Controllers\Api\Media\MediaController;
use App\Http\Controllers\Api\Media\MediaGalleryController;
use App\Http\Controllers\Api\MissingProductOrVendor\MissingProductOrVendorController;
use App\Http\Controllers\Api\MyStack\MyStackCompanyController;
use App\Http\Controllers\Api\MyStack\MyStackCompanyFilterController;
use App\Http\Controllers\Api\MyStack\MyStackCustomerController;
use App\Http\Controllers\Api\MyStack\MyStackUserController;
use App\Http\Controllers\Api\NewsFeed\NewsFeedController;
use App\Http\Controllers\Api\Notification\UserNotificationController;
use App\Http\Controllers\Api\Partner\CompanyPartnerController;
use App\Http\Controllers\Api\Partner\InvitePartnerController;
use App\Http\Controllers\Api\Partner\PartnerAssetsController;
use App\Http\Controllers\Api\Partner\PartnerBrandableContactInfoController;
use App\Http\Controllers\Api\Partner\PartnerCustomerController;
use App\Http\Controllers\Api\Partner\PartnerPageController;
use App\Http\Controllers\Api\Partner\PartnerPageEngagementController;
use App\Http\Controllers\Api\Partner\PartnerPageFilterController;
use App\Http\Controllers\Api\Partner\PartnerPageSectionContentController;
use App\Http\Controllers\Api\Partner\PartnerPageSectionController;
use App\Http\Controllers\Api\Partner\PartnersFilterController;
use App\Http\Controllers\Api\Partner\PartnerTemplateController;
use App\Http\Controllers\Api\PitchEvent\AttendanceController;
use App\Http\Controllers\Api\PitchEvent\BroadcastController;
use App\Http\Controllers\Api\PitchEvent\PitchDayController;
use App\Http\Controllers\Api\PitchEvent\PitchEventPollAnswerController;
use App\Http\Controllers\Api\PitchEvent\PitchEventsController;
use App\Http\Controllers\Api\PitchEvent\Report\ReportController;
use App\Http\Controllers\Api\PitchEvent\VendorScheduleController;
use App\Http\Controllers\Api\Plaid\Company\PlaidCompanyAlertController;
use App\Http\Controllers\Api\Plaid\PlaidCompanyController;
use App\Http\Controllers\Api\Plaid\PlaidController;
use App\Http\Controllers\Api\Plaid\PlaidWebhookController;
use App\Http\Controllers\Api\Poll\PollOptionController;
use App\Http\Controllers\Api\Poll\PollQuestionController;
use App\Http\Controllers\Api\Product\ProductController;
use App\Http\Controllers\Api\Profile\BroadcastMessagesController;
use App\Http\Controllers\Api\Profile\MediaAndDocumentController;
use App\Http\Controllers\Api\Profile\MspProfile\MspProfileContactInfoController;
use App\Http\Controllers\Api\Profile\MspProfile\MspProfileController;
use App\Http\Controllers\Api\Profile\ProfileController;
use App\Http\Controllers\Api\Profile\ProfileRuleValueController;
use App\Http\Controllers\Api\Profile\UserProfileController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileBrandableController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileContactInfoController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileContactsController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileDocumentController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileImageController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileOverviewController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfilePeopleController;
use App\Http\Controllers\Api\Profile\VendorBasicProfile\VendorProfileVideoController;
use App\Http\Controllers\Api\ProfileEnrichment\ProfileEnrichmentCompanyAnswersController;
use App\Http\Controllers\Api\ProfileEnrichment\ProfileEnrichmentController;
use App\Http\Controllers\Api\Question\QuestionAnswerController;
use App\Http\Controllers\Api\Question\QuestionController;
use App\Http\Controllers\Api\Question\QuestionFormController;
use App\Http\Controllers\Api\Refer\ReferController;
use App\Http\Controllers\Api\Review\ReviewAnswerController;
use App\Http\Controllers\Api\Review\ReviewController;
use App\Http\Controllers\Api\Review\ReviewReplyController;
use App\Http\Controllers\Api\Search\SearchController;
use App\Http\Controllers\Api\Search\SimpleSearchController;
use App\Http\Controllers\Api\SendUpcomingEvents\SendUpcomingEventsController;
use App\Http\Controllers\Api\Shortener\ShortenerController;
use App\Http\Controllers\Api\ShoutOut\ShoutOutController;
use App\Http\Controllers\Api\Social\SocialMediaController;
use App\Http\Controllers\Api\Tag\TagController;
use App\Http\Controllers\Api\TimeZone\TimeZoneController;
use App\Http\Controllers\Api\User\UserCompanyRequestController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VendorOnboardingController;
use App\Http\Controllers\Api\VendorSiteSeo\VendorSiteSeoController;
use App\Http\Controllers\Api\VendorSiteSeo\VendorSiteSeoSocialController;
use App\Http\Controllers\Api\VideoController;
use App\Http\Controllers\Api\Whitelabeling\WhitelabelingController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\RegisterMSPController;
use App\Http\Controllers\Auth\RegisterUserController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\TwoFALoginController;
use App\Http\Controllers\LookupOptionController;
use Illuminate\Support\Facades\Route;
use Laravel\Fortify\Features;
use Laravel\Fortify\Http\Controllers\RecoveryCodeController;
use Laravel\Fortify\Http\Controllers\TwoFactorAuthenticationController;
use Laravel\Fortify\Http\Controllers\TwoFactorQrCodeController;
use Laravel\Fortify\Http\Controllers\TwoFactorSecretKeyController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// EXAMPLE FOR LATER VERSIONS (https://github.com/mbpcoder/laravel-api-versioning)
// Route::prefix('v1.1')->group(function () {
//     Route::middleware('jwt.verify')->group(function () {
//        Route::post('/company/full', [CompanyController::class, 'storeFullVersion2']);
// });
// });

$limiter = config('fortify.limiters.login');
$twoFactorLimiter = config('fortify.limiters.two-factor');
$twoFactorMiddleware = Features::optionEnabled(Features::twoFactorAuthentication(), 'confirmPassword')
    ? ['auth', 'password.confirm']
    : ['auth'];

Route::prefix('v1')->group(function () use ($twoFactorMiddleware) {
    // put in this group if api needs authentication
    Route::middleware(['jwt.verify', 'verified'])->group(function () use ($twoFactorMiddleware) {
        Route::post('/logout', [AuthenticatedSessionController::class, 'destroy']);
        Route::get('/enum/private/{enumName}', [EnumController::class, 'showByEnumPrivate']);
        Route::post('/changepassword', [ChangePasswordController::class, 'store']);
        Route::post('/changeemail', [EmailChangeController::class, 'change']);
        Route::post('/validatepassword', [TwoFALoginController::class, 'validatePassword']);
        Route::post('/register-for-all-events', [RegisterUserController::class, 'registerForAllEvents']);
        // Admin API
        Route::prefix('admin')->group(function () {
            Route::prefix('channel-deals')->group(function () {
                Route::post('/store', [AdminChannelDealsController::class, 'store'])->name('admin.channel-deals.store');
                Route::prefix('{channelDeal}')->where(['channelDeal' => '[0-9]+'])->group(function () {
                    Route::post('/update', [AdminChannelDealsController::class, 'update'])->name('admin.channel-deals.update');
                });

                Route::prefix('claimed')->group(function () {
                    Route::get('/', [AdminChannelDealsClaimedController::class, 'showAll'])->name('admin.channel-deals.claimed.show-all');
                    Route::get('/filters', [AdminChannelDealsClaimedController::class, 'showAllFilters'])->name('admin.channel-deals.claimed.show-all-filters');

                    Route::prefix('{channelDealsClaimed}')->where(['channelDeal' => '[0-9]+'])->group(function () {
                        Route::put('/update', [AdminChannelDealsClaimedController::class, 'update'])->name('admin.channel-deals.claimed.update');
                    });
                });
            });
            Route::prefix('user-content')->group(function () {
                Route::get('reject-options', [AdminUserContentController::class, 'showAllRejectOptions'])->name('admin.user-content.reject-options');
                Route::get('filters', [AdminUserContentController::class, 'showFilters'])->name('admin.user-content.show-all-filters');
                Route::get('count-pending', [AdminUserContentController::class, 'showPendingContentCount'])->name('admin.user-content.count-pending');
                Route::post('reject', [AdminUserContentController::class, 'rejectEntities'])->name('admin.user-content.reject-entities');

                Route::prefix('companies')->group(function () {
                    Route::get('show-all', [AdminUserContentController::class, 'showAllCompanies'])->name('admin.user-content.companies.show-all');
                    Route::prefix('{company_id}')->where(['company_id' => '[0-9]+'])->group(function () {
                        Route::post('approve', [AdminUserContentController::class, 'approveCompany'])->name('admin.user-content.companies.approve');
                        Route::post('avatar/store', [AdminUserContentController::class, 'updateCompanyAvatar'])->name('admin.user-content.companies.avatar.store');
                    });
                });
                Route::prefix('products')->group(function () {
                    Route::get('show-all', [AdminUserContentController::class, 'showAllProducts'])->name('admin.user-content.products.show-all');
                    Route::post('{product_id}/approve', [AdminUserContentController::class, 'approveProduct'])->name('admin.user-content.products.approve');
                });
            });

            Route::post('/send-upcoming-events', [SendUpcomingEventsController::class, 'sendEvents']);
            Route::post('/update-customer-claimer', [CyclrController::class, 'addOrUpdateCustomerClaimer']);
            Route::prefix('maint')->group(function () {
                Route::prefix('artisan')->group(function () {
                    Route::post('/run', [ArtisanController::class, 'run']);
                });
            });
            Route::prefix('activity-logs')->group(function () {
                Route::get('', [AdminActivityLogsController::class, 'showAll']);
            });
            // Advertisement
            Route::prefix('advertisement')->group(function () {
                Route::get('', [AdminAdvertisementController::class, 'showAll']);
                Route::post('store', [AdminAdvertisementController::class, 'store']);
                Route::post('update', [AdminAdvertisementController::class, 'update']);
                Route::delete('delete', [AdminAdvertisementController::class, 'delete']);
                Route::get('find-vendors', [AdminAdvertisementController::class, 'findVendors']);
                Route::get('find-events', [AdminAdvertisementController::class, 'findEvents']);
                Route::prefix('card-type')->group(function () {
                    Route::get('', [AdminAdvertisementCardTypeController::class, 'showAll']);
                });

                Route::prefix('activity-logs')->group(function () {
                    Route::get('', [AdminActivityLogsController::class, 'showAll']);
                });
            });
            Route::prefix('notifications')->group(function () {
                Route::get('', [AdminNotificationController::class, 'showAll']);
            });
            // Comments
            Route::prefix('comments')->group(function () {
                Route::get('', [AdminCommentController::class, 'showAll']);
                Route::get('', [AdminCommentController::class, 'showAll']);
                Route::put('approve', [AdminCommentController::class, 'approve']);
                Route::put('reject', [AdminCommentController::class, 'reject']);
            });
            // Currencies
            Route::prefix('currencies')->group(function () {
                Route::get('', [AdminCurrencyController::class, 'showAll']);
                Route::post('store', [AdminCurrencyController::class, 'store']);
                Route::prefix('{currency}')->where(['currency' => '[0-9]+'])->group(function () {
                    Route::get('', [AdminCurrencyController::class, 'show']);
                    Route::put('update', [AdminCurrencyController::class, 'update']);
                });
            });

            Route::prefix('company')->group(function () {
                Route::get('', [AdminCompanyController::class, 'showAll']);
                Route::get('filters', [AdminCompanyController::class, 'showAllFilters']);
                Route::post('sync-all-hubspot', [AdminCompanyController::class, 'syncAllHubspot']);
                Route::post('sync-company-categories', [AdminCompanyController::class, 'syncCompanyCategoriesToHubspot']);
                Route::post('sync-company-url', [AdminCompanyController::class, 'syncCompanyUrlToHubspot']);
                Route::post('pull-hubspot-deals', [AdminCompanyController::class, 'pullAllCompaniesHubspotDeals']);
                Route::prefix('claimers')->group(function () {
                    Route::prefix('responses')->group(function () {
                        Route::get('', [AdminCompanyClaimerResponseController::class, 'showAll']);
                        Route::put('change-status', [AdminCompanyClaimerResponseController::class, 'changeStatus']);
                        Route::get('export-to-csv', [AdminCompanyClaimerResponseController::class, 'exportToCsv']);
                    });
                });
                Route::prefix('locations')->group(function () {
                    Route::get('', [AdminCompanyLocationController::class, 'showAll']);
                    Route::get('available-parents', [AdminCompanyLocationController::class, 'loadAvailableParents']);
                    Route::get('available-roles', [AdminCompanyLocationController::class, 'loadAvailableRoles']);
                    Route::post('store', [AdminCompanyLocationController::class, 'store']);
                });
                Route::post('hs/subscription', [AdminCompanyDetailController::class, 'changeSubscriptionHS']);
                Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                    Route::get('affiliates-filters', [AdminCompanyController::class, 'showAffiliatesFilters']);
                    Route::post('open-invitation/refresh', [AdminCompanyController::class, 'refreshVendorOpenInvitation']);
                    Route::get('details', [AdminCompanyDetailController::class, 'showDetails']);
                    Route::put('update', [AdminCompanyDetailController::class, 'update']);
                    Route::prefix('users-and-claimers')->group(function () {
                        Route::get('', [AdminCompanyDetailController::class, 'getCompanyUsersAndClaimers']);
                        Route::get('filters', [AdminCompanyDetailController::class, 'getCompanyUsersAndClaimersFilters']);
                    });
                    Route::post('sync-hubspot-id', [AdminCompanyDetailController::class, 'syncHubspotId']);
                    Route::post('subscription', [AdminCompanyDetailController::class, 'changeSubscription']);
                    Route::post('pull-hubspot-deals', [AdminCompanyDetailController::class, 'pullCompanyHubspotDeals']);
                    Route::post('add-user-role', [AdminCompanyDetailController::class, 'addUserRole']);
                    Route::post('update-user-role', [AdminCompanyDetailController::class, 'updateUserRole']);
                    Route::delete('remove-user-role', [AdminCompanyDetailController::class, 'removeUserRole']);
                    Route::prefix('claimers')->group(function () {
                        Route::get('search-users', [AdminCompanyClaimerController::class, 'searchUsers']);
                        Route::delete('delete', [AdminCompanyClaimerController::class, 'delete']);
                    });
                    Route::put('partner-flag', [AdminCompanyController::class, 'companyPartnerPortalFlag']);
                    Route::put('is-distributor', [AdminCompanyController::class, 'updateCompanyIsDistributor']);
                    Route::put('manage-clients', [AdminCompanyController::class, 'updateCompanyManageClients']);
                    Route::put('manage-expenses', [AdminCompanyController::class, 'updateCompanyManageExpenses']);
                    Route::put('manage-affiliates', [AdminCompanyController::class, 'updateCompanyManageAffiliates']);
                });
            });
            Route::prefix('configuration')->group(function () {
                Route::post('app/store', [AppConfigurationController::class, 'store']);
                Route::delete('app/delete', [AppConfigurationController::class, 'delete']);
                Route::put('app/update', [AppConfigurationController::class, 'update']);
                Route::get('app', [AppConfigurationController::class, 'showAll']);
            });
            Route::prefix('cpjobs')->group(function () {
                Route::get('list', [CpJobsController::class, 'showAll']);
                Route::get('{job_id}', [CpJobsController::class, 'getJobsById'])
                    ->where(['job_id' => '[0-9]+']);
                Route::put('update', [CpJobsController::class, 'update']);
                Route::get('execute/{job_id}', [CpJobsController::class, 'executeJobs'])
                    ->where(['job_id' => '[0-9]+']);
                Route::get('flush', [CpJobsController::class, 'flushJobs']);
            });
            Route::prefix('migrations')->group(function () {
                Route::prefix('contracts')->group(function () {
                    Route::post('migrate', [MigrateContractsController::class, 'migrateContracts']);
                    Route::post('rollback', [MigrateContractsController::class, 'rollbackContracts']);
                    Route::post('rename-migrated-contracts', [MigrateContractsController::class, 'renameMigratedContracts']);
                    Route::post('check-consistency', [MigrateContractsController::class, 'checkConsistency']);
                    Route::post('rename-vendor-contracts', [MigrateContractsController::class, 'renameVendorContracts']);
                });
            });
            Route::prefix('analytics')->group(function () {
                Route::prefix('events')->group(function () {
                    Route::get('attendeecount', [EventAnalyticsController::class, 'eventAttendeeCount']);
                });
                Route::prefix('video-views')->group(function () {
                    Route::get('', [ViewCountsController::class, 'showAll']);
                    Route::prefix('{video}')->where(['video' => '[0-9]+'])->group(function () {
                        Route::get('details', [ViewCountsDetailController::class, 'showDetails']);
                        Route::post('views-by-date', [ViewCountsDetailController::class, 'viewsByDate']);
                        Route::post('generate-csv', [ViewCountsDetailController::class, 'generateCSV']);
                    });
                });
            });
            Route::prefix('customview')->group(function () {
                Route::get('/', [CustomViewController::class, 'showAll']);
                Route::post('/createreport', [CustomViewController::class, 'createReport']);
                Route::post('/store', [CustomViewController::class, 'store']);
                Route::delete('/delete', [CustomViewController::class, 'delete']);
            });
            Route::prefix('email')->group(function () {
                Route::get('', [AdminEmailController::class, 'showAll']);
                Route::post('store', [AdminEmailController::class, 'store']);
                Route::put('update', [AdminEmailController::class, 'update']);
                Route::delete('delete', [AdminEmailController::class, 'delete']);
                Route::get('{email}', [AdminEmailController::class, 'detail'])->where(['email' => '[0-9]+']);
                Route::prefix('allowed-parameter')->group(function () {
                    Route::get('', [AdminEmailAllowedParameterController::class, 'showAll']);
                    Route::post('store', [AdminEmailAllowedParameterController::class, 'store']);
                    Route::put('update', [AdminEmailAllowedParameterController::class, 'update']);
                    Route::delete('delete', [AdminEmailAllowedParameterController::class, 'delete']);
                });
                Route::prefix('{email}/template-text')->where(['email' => '[0-9]+'])->group(function () {
                    Route::get('', [AdminEmailTemplateTextController::class, 'showAll']);
                    Route::post('store', [AdminEmailTemplateTextController::class, 'store']);
                    Route::put('update', [AdminEmailTemplateTextController::class, 'update']);
                    Route::delete('delete', [AdminEmailTemplateTextController::class, 'delete']);
                });
            });
            Route::prefix('emailwhitelist')->group(function () {
                Route::post('/store', [EmailWhitelistController::class, 'store']);
                Route::delete('/delete', [EmailWhitelistController::class, 'delete']);
                Route::delete('/domain/delete', [EmailWhitelistController::class, 'deleteByDomain']);
                Route::get('', [EmailWhitelistController::class, 'showAll']);
            });
            Route::prefix('affiliate-brands')->group(function () {
                Route::get('', [AdminAffiliateBrandsController::class, 'showAll']);
                Route::get('filters', [AdminAffiliateBrandsController::class, 'showAllFilters']);
                Route::delete('delete', [AdminAffiliateBrandsController::class, 'delete']);
                Route::post('store', [AdminAffiliateBrandsController::class, 'store']);
                Route::put('update', [AdminAffiliateBrandsController::class, 'update']);
            });
            Route::prefix('user')->group(function () {
                Route::get('', [AdminUserController::class, 'showAll']);
                Route::get('filters', [AdminUserController::class, 'showAllFilters']);
                Route::get('{user}/details', [AdminUserController::class, 'userDetails']);
                Route::post('count', [AdminUserController::class, 'showUserCount']);
                Route::get('get-counts', [AdminUserController::class, 'getUserCounts']);
                Route::get('get-user-by-email', [AdminUserController::class, 'getUserByEmail']);
                Route::delete('delete', [AdminUserController::class, 'delete']);
                Route::post('change-status', [AdminUserController::class, 'changeStatus']);
                Route::post('change-email', [AdminUserController::class, 'changeEmail']);
                Route::get('companies', [AdminUserController::class, 'getCompanies']);
                Route::post('login-history', [AdminUserController::class, 'loginHistory']);
                Route::post('create-msp-with-csv', [AdminUserController::class, 'createMSPUsersWithCSV']);
                Route::put('reset-two-factor-authentication', [AdminUserController::class, 'resetTwoFactorAuthentication']);
                Route::prefix('requests')->group(function () {
                    Route::get('filters', [UserCompanyRequestController::class, 'adminShowAllFilters']);
                    Route::post('search', [UserCompanyRequestController::class, 'adminSearchRequests']);
                    Route::put('flag', [UserCompanyRequestController::class, 'adminFlagRequest']);
                });
            });
            Route::prefix('jobtitle')->group(function () {
                Route::post('/store', [AdminJobTitleController::class, 'store']);
                Route::put('/update', [AdminJobTitleController::class, 'update']);
                Route::delete('/delete', [AdminJobTitleController::class, 'delete']);
            });
            Route::prefix('chart')->group(function () {
                Route::prefix('metric')->group(function () {
                    Route::get('/', [AdminChartMetricController::class, 'showAll']);
                    Route::post('/store', [AdminChartMetricController::class, 'store']);
                    Route::put('/update', [AdminChartMetricController::class, 'update']);
                    Route::delete('/delete', [AdminChartMetricController::class, 'delete']);
                });
                Route::prefix('pitch-poll')->group(function () {
                    Route::get('/', [AdminChartPitchPollController::class, 'showAll']);
                    Route::post('/sync', [AdminChartPitchPollController::class, 'sync']);
                });
                Route::prefix('quadrant')->group(function () {
                    Route::post('load-job-data', [AdminChartQuadrantController::class, 'loadJobData']);
                });
                Route::prefix('vendor')->group(function () {
                    Route::get('/', [AdminChartVendorController::class, 'showAll']);
                    Route::post(
                        'hide-from-all-charts',
                        [AdminChartVendorController::class, 'hideVendorFromAllCharts']
                    );
                    Route::post('show-on-all-charts', [AdminChartVendorController::class, 'showVendorOnAllCharts']);
                    Route::post('hide-from-charts', [AdminChartVendorController::class, 'hideVendorFromCharts']);
                    Route::post('show-on-charts', [AdminChartVendorController::class, 'showVendorOnCharts']);
                });

                Route::prefix('pitch-event')->group(function () {
                    Route::get('/', [AdminChartPitchEventController::class, 'showAll']);
                    Route::post(
                        'hide-from-all-charts',
                        [AdminChartPitchEventController::class, 'hidePitchEventFromAllCharts']
                    );
                    Route::post(
                        'show-on-all-charts',
                        [AdminChartPitchEventController::class, 'showPitchEventOnAllCharts']
                    );
                    Route::post(
                        'hide-from-charts',
                        [AdminChartPitchEventController::class, 'hidePitchEventFromCharts']
                    );
                    Route::post(
                        'show-on-charts',
                        [AdminChartPitchEventController::class, 'showPitchEventOnCharts']
                    );
                });
            });
            Route::prefix('profanity')->group(function () {
                Route::post('/store', [ProfanityFilterController::class, 'store']);
                Route::put('/update', [ProfanityFilterController::class, 'update']);
                Route::delete('/delete', [ProfanityFilterController::class, 'delete']);
            });
            Route::prefix('profile')->group(function () {
                Route::get('', [AdminProfileController::class, 'showAll']);
                Route::get('company', [AdminProfileController::class, 'showAllForCompany']);
                Route::get('user', [AdminProfileController::class, 'showAllForUser']);
                Route::prefix('rule')->group(function () {
                    Route::get('', [AdminProfileRuleController::class, 'showAll']);
                    Route::prefix('value')->group(function () {
                        Route::get('', [AdminProfileRuleValueController::class, 'showAll']);
                        Route::post('store', [AdminProfileRuleValueController::class, 'store']);
                        Route::put('update', [AdminProfileRuleValueController::class, 'update']);
                        Route::delete('delete', [AdminProfileRuleValueController::class, 'delete']);
                    });
                });
            });
            Route::prefix('redis')->group(function () {
                Route::post('flush', [RedisController::class, 'flush']);
                Route::post('flush/permissions_and_roles', [RedisController::class, 'flushPermissionsAndRoles']);
            });
            Route::prefix('categories')->group(function () {
                Route::get('', [AdminCategoryController::class, 'showAll']);
                Route::get('filters', [AdminCategoryController::class, 'showAllFilters']);
                Route::post('store', [AdminCategoryController::class, 'store']);
                Route::put('update', [AdminCategoryController::class, 'update']);
                Route::delete('delete', [AdminCategoryController::class, 'delete']);
            });
            Route::prefix('permission')->group(function () {
                Route::get('', [AdminPermissionController::class, 'showAll']);
                Route::get('filters', [AdminPermissionController::class, 'showAllFilters']);
                Route::post('store', [AdminPermissionController::class, 'store']);
                Route::put('update', [AdminPermissionController::class, 'update']);
                Route::delete('delete', [AdminPermissionController::class, 'delete']);
                Route::prefix('ctpfgf')->group(function () {
                    Route::get('', [AdminCompanyTypePFGFeatureController::class, 'showAll']);
                });
                Route::prefix('feature')->group(function () {
                    Route::get('', [AdminPermissionFeatureController::class, 'showAll']);
                    Route::post('store', [AdminPermissionFeatureController::class, 'store']);
                    Route::put('update', [AdminPermissionFeatureController::class, 'update']);
                    Route::delete('delete', [AdminPermissionFeatureController::class, 'delete']);
                    Route::prefix('group')->group(function () {
                        Route::get('', [AdminPermissionFeatureGroupController::class, 'showAll']);
                        Route::post('store', [AdminPermissionFeatureGroupController::class, 'store']);
                        Route::put('update', [AdminPermissionFeatureGroupController::class, 'update']);
                        Route::delete('delete', [AdminPermissionFeatureGroupController::class, 'delete']);
                    });
                });
                Route::prefix('role')->group(function () {
                    Route::get('', [AdminRoleController::class, 'showAll']);
                    Route::post('store', [AdminRoleController::class, 'store']);
                    Route::put('update', [AdminRoleController::class, 'update']);
                    Route::post('duplicate', [AdminRoleController::class, 'duplicate']);
                    Route::delete('delete', [AdminRoleController::class, 'delete']);
                });
                Route::prefix('template-role')->group(function () {
                    Route::get('', [AdminTemplateRoleController::class, 'showAll']);
                    Route::get('filters', [AdminTemplateRoleController::class, 'showAllFilters']);
                    Route::post('store', [AdminTemplateRoleController::class, 'store']);
                    Route::put('update', [AdminTemplateRoleController::class, 'update']);
                    Route::delete('delete', [AdminTemplateRoleController::class, 'delete']);
                });
            });
            Route::prefix('profile-enrichment')->group(function () {
                Route::prefix('questions')->group(function () {
                    Route::get('', [AdminProfileEnrichmentQuestionController::class, 'showAll']);
                    Route::post('store', [AdminProfileEnrichmentQuestionController::class, 'store']);
                    Route::put('update', [AdminProfileEnrichmentQuestionController::class, 'update']);
                    Route::delete('delete', [AdminProfileEnrichmentQuestionController::class, 'delete']);
                    Route::prefix('{question}')->where(['question' => '[0-9]+'])->group(function () {
                        Route::prefix('options')->group(function () {
                            Route::get('', [AdminProfileEnrichmentOptionController::class, 'showAll']);
                            Route::post('store', [AdminProfileEnrichmentOptionController::class, 'store']);
                            Route::put('update', [AdminProfileEnrichmentOptionController::class, 'update']);
                            Route::delete('delete', [AdminProfileEnrichmentOptionController::class, 'delete']);
                        });
                    });
                });
            });
            Route::prefix('questions')->group(function () {
                Route::get('', [AdminQuestionController::class, 'showAll']);
                Route::post('store', [AdminQuestionController::class, 'store']);
                Route::put('update', [AdminQuestionController::class, 'update']);
                Route::delete('delete', [AdminQuestionController::class, 'delete']);
                Route::prefix('{question}')->where(['question' => '[0-9]+'])->group(function () {
                    Route::prefix('options')->group(function () {
                        Route::get('', [AdminQuestionOptionController::class, 'showAll']);
                        Route::post('store', [AdminQuestionOptionController::class, 'store']);
                        Route::put('update', [AdminQuestionOptionController::class, 'update']);
                        Route::delete('delete', [AdminQuestionOptionController::class, 'delete']);
                    });
                });
                Route::prefix('questionformtype')->group(function () {
                    Route::get('', [AdminQuestionFormTypeController::class, 'showAll']);
                    Route::post('store', [AdminQuestionFormTypeController::class, 'store']);
                    Route::delete('delete', [AdminQuestionFormTypeController::class, 'delete']);
                });
                Route::prefix('form')->group(function () {
                    Route::get('', [AdminQuestionFormController::class, 'showAll']);
                    Route::post('store', [AdminQuestionFormController::class, 'store']);
                    Route::put('update', [AdminQuestionFormController::class, 'update']);
                    Route::delete('delete', [AdminQuestionFormController::class, 'delete']);
                });
            });
            Route::prefix('lookups')->group(function () {
                Route::get('', [AdminLookupOptionController::class, 'showAll']);
                Route::post('store', [AdminLookupOptionController::class, 'store']);
                Route::put('update', [AdminLookupOptionController::class, 'update']);
                Route::delete('delete', [AdminLookupOptionController::class, 'delete']);
                Route::prefix('{lookupOption}')->where(['lookupOption' => '[a-zA-Z0-9-_]+'])->group(function () {
                    Route::prefix('options')->group(function () {
                        Route::get('', [AdminLookupOptionValuesController::class, 'showAll']);
                        Route::post('store', [AdminLookupOptionValuesController::class, 'store']);
                        Route::put('update', [AdminLookupOptionValuesController::class, 'update']);
                        Route::delete('delete', [AdminLookupOptionValuesController::class, 'delete']);
                    });
                });
            });
            Route::prefix('review')->group(function () {
                Route::prefix('{review}')->where(['review' => '[0-9]+'])->group(function () {
                    Route::get('', [AdminReviewController::class, 'loadReviewById']);
                });
                Route::prefix('products')->group(function () {
                    Route::get('', [AdminProductReviewController::class, 'loadProductReviews']);
                    Route::get('export-to-csv', [AdminProductReviewController::class, 'exportToCSV']);
                    Route::post('move', [AdminProductReviewController::class, 'moveReviews']);
                });
                Route::prefix('incentives')->group(function () {
                    Route::get('{reviewer}', [AdminReviewIncentiveController::class, 'loadIncentivesByReviewerId'])->where(['reviewer' => '[0-9]+']);
                });
                Route::put('incentive-update', [AdminReviewController::class, 'incentiveUpdate']);
                Route::put('approve', [AdminReviewController::class, 'approve']);
                Route::put('under-review', [AdminReviewController::class, 'underReview']);
                Route::put('flag', [AdminReviewController::class, 'flag']);
                Route::delete('delete-abandoned-archived-review', [AdminReviewController::class, 'deleteAbandonedArchivedReview']);
                Route::post('update-review-status', [AdminReviewController::class, 'updateMultipleReviewStatus']);
                Route::prefix('questions')->group(function () {
                    Route::get('', [AdminReviewQuestionController::class, 'showAll']);
                    Route::post('store', [AdminReviewQuestionController::class, 'store']);
                    Route::put('update', [AdminReviewQuestionController::class, 'update']);
                    Route::delete('delete', [AdminReviewQuestionController::class, 'delete']);
                    Route::get('region/options', [AdminReviewQuestionOptionController::class, 'loadRegionQuestionOptions']);
                    Route::prefix('{question}')->where(['question' => '[0-9]+'])->group(function () {
                        Route::prefix('options')->group(function () {
                            Route::get('', [AdminReviewQuestionOptionController::class, 'showAll']);
                            Route::post('store', [AdminReviewQuestionOptionController::class, 'store']);
                            Route::put('update', [AdminReviewQuestionOptionController::class, 'update']);
                            Route::delete('delete', [AdminReviewQuestionOptionController::class, 'delete']);
                        });
                    });
                });
            });
            Route::prefix('tango')->group(function () {
                Route::prefix('api')->group(function () {
                    Route::get('{api_name}', [TangoApiController::class, 'apiInterface']);
                });

                Route::prefix('catalog')->group(function () {
                    Route::get('', [TangoCatalogController::class, 'showAll']);
                    Route::post('store', [TangoCatalogController::class, 'store']);
                    Route::put('update', [TangoCatalogController::class, 'update']);
                    Route::delete('delete', [TangoCatalogController::class, 'delete']);
                });
            });
            Route::prefix('reports')->group(function () {
                Route::prefix('pitch')->group(function () {
                    Route::get('all-pitch-poll-results', [ReportController::class, 'allPitchPollResults']);
                    Route::get('mspusers/{pitch_event_id}', [ReportController::class, 'showPitchMSPUsers']);
                    Route::get('vendorusers/{pitch_event_id}', [ReportController::class, 'showPitchVendorUsers']);
                });
                Route::prefix('product')->group(function () {
                    Route::prefix('review')->group(function () {
                        Route::get('', [AdminProductReviewController::class, 'loadProductReviewsReport']);
                        Route::get('export-count-of-reviews-by-vendor-to-csv', [AdminProductReviewController::class, 'exportCountOfReviewsByVendorToCsv']);
                        Route::get('export-count-of-reviews-by-product-to-csv', [AdminProductReviewController::class, 'exportCountOfReviewsByProductToCsv']);
                    });
                });
            });
            Route::prefix('video')->group(function () {
                Route::post('{vendor}/add-to-vendor', [VideoController::class, 'uploadVideoToVendorProfile']);
                Route::post('{video}/update-video-thumbnail/', [VideoController::class, 'updateVideoThumbnail'])
                    ->where('video', '[0-9]+');
            });
            Route::prefix('industry-events')->group(function () {
                Route::get('', [AdminIndustryEventsController::class, 'showAll']);
                Route::post('store', [AdminIndustryEventsController::class, 'store']);
                Route::post('update', [AdminIndustryEventsController::class, 'update']);
                Route::post('check-feature-flag', [AdminIndustryEventsController::class, 'checkFeatureFlag']);
                Route::prefix('bulk')->group(function () {
                    Route::delete('delete', [AdminIndustryEventsController::class, 'delete']);
                    Route::put('update-status', [AdminIndustryEventsController::class, 'updateStatus']);
                });
                Route::get('details', [AdminIndustryEventsController::class, 'details']);
            });
            // Tags
            Route::prefix('tags')->group(function () {
                Route::get('search', [AdminTagController::class, 'search']);
                Route::get('filters', [AdminTagController::class, 'filters']);
                Route::post('store', [AdminTagController::class, 'store']);
                Route::put('update', [AdminTagController::class, 'update']);
            });
            Route::prefix('two-factor')->group(function () {
                Route::delete('/delete', [TwoFALoginController::class, 'delete']);
            });
            // Focuses
            Route::prefix('focuses')->group(function () {
                Route::post('/store', [AdminFocusController::class, 'store']);
                Route::prefix('/{focus}')->where(['focus' => '[0-9]+'])->group(function () {
                    Route::delete('/delete', [AdminFocusController::class, 'delete']);
                    Route::post('/update', [AdminFocusController::class, 'update']);
                    Route::prefix('/options')->group(function () {
                        Route::get('/', [AdminFocusOptionController::class, 'showAll']);
                        Route::post('/store', [AdminFocusOptionController::class, 'store']);
                        Route::prefix('/{option}')->where(['option' => '[0-9]+'])->group(function () {
                            Route::delete('/delete', [AdminFocusOptionController::class, 'delete']);
                            Route::post('/update', [AdminFocusOptionController::class, 'update']);
                        });
                    });
                });
            });
            // Social Media
            Route::prefix('social-media')->group(function () {
                Route::get('', [AdminSocialMediaController::class, 'showAll']);
                Route::post('store', [AdminSocialMediaController::class, 'store']);
                Route::put('update', [AdminSocialMediaController::class, 'update']);
                Route::delete('delete', [AdminSocialMediaController::class, 'delete']);
            });
            Route::prefix('broadcast-messages')->group(function () {
                Route::get('', [AdminBroadcastMessagesController::class, 'showAll']);
                Route::post('/store', [AdminBroadcastMessagesController::class, 'store']);
            });
            // Pitch events
            Route::prefix('pitch-events')->group(function () {
                Route::get('past', [AdminPitchEventController::class, 'showAllPast']);
                Route::prefix('msp-attendants')->group(function () {
                    Route::get('', [AdminPitchEventMSPAttendantsController::class, 'showAllMSPAttendants']);
                    Route::put('/update', [AdminPitchEventMSPAttendantsController::class, 'update']);
                });
                Route::prefix('questions')->group(function () {
                    Route::get('', [AdminPitchEventQuestionsController::class, 'showAll']);
                });
                Route::prefix('/{pitchEvent}')->where(['pitchEvent' => '[0-9]+'])->group(function () {
                    Route::get('/report/complete/{vendor}', [ReportController::class, 'complete'])
                        ->where('vendor', '[0-9]+');
                });
            });
            Route::prefix('pitch-day')->group(function () {
                Route::post('/load-info', [AdminPitchDayController::class, 'loadInfo']);
                Route::post('/load-public-chat-msgs', [AdminPitchDayController::class, 'loadPublicChatMessages']);
                Route::post('/send-message-to-public', [AdminPitchDayController::class, 'sendMessageToPublic']);
                Route::post('/delete-public-chat-msg', [AdminPitchDayController::class, 'deletePublicChatMsg']);
                Route::post('/ban-user-public-chat-msgs', [AdminPitchDayController::class, 'banUserPublicChatMsgs']);
            });
            Route::get('/contract-and-customer-stack-migration', [ContractAndCustomerStackMigrationController::class, 'migrate']);
        });

        // Focus
        Route::prefix('focus')->group(function () {
            Route::get('{company:friendly_url}', [FocusController::class, 'showAll']);
        });

        Route::prefix('cyclr')->group(function () {
            Route::post('save-result', [CyclrController::class, 'saveResult']);
            Route::get('get-embed-url/{company}', [CyclrController::class, 'getEmbedUrl'])
                ->name('api.cyclr.get-embed-url')
                ->where('company', '[0-9]+');
        });

        Route::prefix('channel-deals')->group(function () {
            Route::get('/', [ChannelDealsController::class, 'showAll'])->name('channel-deals.show-all');
            Route::post('/store', [ChannelDealsController::class, 'store'])->name('channel-deals.store');
            Route::prefix('{channelDeal}')->where(['channelDeal' => '[0-9]+'])->group(function () {
                Route::post('/update', [ChannelDealsController::class, 'update'])->name('channel-deals.update');
                Route::delete('/delete', [ChannelDealsController::class, 'delete'])->name('channel-deals.delete');
                // Logs
                Route::get('/logs', [ChannelDealLogController::class, 'loadLogs'])->name('channel-deals.load-logs');
                Route::get('/logs/filters', [ChannelDealLogController::class, 'loadLogsFilters'])->name('channel-deals.logs-filters');
                Route::get('/logs/csv', [ChannelDealLogController::class, 'exportLogsToCsv'])->name('channel-deals.export-logs');
            });
        });

        Route::prefix('question')->group(function () {
            Route::get('questions', [QuestionController::class, 'loadQuestions']);
            Route::prefix('{question}')->where(['question' => '[0-9]+'])->group(function () {
                Route::prefix('answer')->group(function () {
                    Route::get('', [QuestionAnswerController::class, 'showAll']);
                    Route::post('store', [QuestionAnswerController::class, 'store']);
                    Route::put('update', [QuestionAnswerController::class, 'update']);
                    Route::delete('delete', [QuestionAnswerController::class, 'delete']);
                });
            });
            Route::prefix('form')->group(function () {
                Route::get('', [QuestionFormController::class, 'show']);
            });
        });

        // Integrations
        Route::prefix('integrations')->group(function () {
            Route::post('', [IntegrationController::class, 'store']);
            Route::get('', [IntegrationController::class, 'showAll']);
            Route::get('contract-fields', [IntegrationController::class, 'showAllContractFields']);
            Route::get('contract-billing-types', [IntegrationController::class, 'showAllContractBillingTypes']);
            Route::get('contract-recurrence-intervals', [IntegrationController::class, 'showAllContractRecurrenceIntervals']);
            Route::get('contract-billing-contacts', [IntegrationController::class, 'showAllContractBillingContacts']);
            Route::get('contract-agreements', [IntegrationController::class, 'showAllContractAgreements']);
        });

        // Deals
        Route::prefix('deals')->group(function () {
            Route::prefix('interactions')->group(function () {
                Route::post('options', [DealController::class, 'showDealInteractionsOptions']);
            });
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::post('store', [DealController::class, 'store']);
                Route::put('update', [DealController::class, 'updateDeal']);
                Route::delete('delete', [DealController::class, 'delete']);
                Route::get('filters', [DealController::class, 'showAllFilters']);
                Route::post('search', [DealController::class, 'searchDeals']);
                Route::get('totals', [DealController::class, 'loadTotals']);
                Route::post('export-to-csv', [DealController::class, 'exportToCsv']);
                Route::post('config-expiration', [DealController::class, 'configExpirationDate']);
                Route::post('approve', [DealController::class, 'approve']);
                Route::post('decline', [DealController::class, 'decline']);
                Route::post('withdraw', [DealController::class, 'withdraw']);
                Route::post('request-info', [DealController::class, 'requestInfo']);
                Route::prefix('contacts')->group(function () {
                    Route::get('', [DealController::class, 'showAllNotificationContacts']);
                    Route::post('store', [DealController::class, 'storeNotificationContacts']);
                    Route::delete('delete', [DealController::class, 'deleteNotificationContacts']);
                });

                Route::prefix('/deal/{deal}')->where(['deal' => '[0-9]+'])->group(function () {
                    Route::get('', [DealController::class, 'loadDeal']);
                    Route::prefix('documents')->group(function () {
                        Route::get('', [DealDocumentController::class, 'showAll']);
                        Route::post('store', [DealDocumentController::class, 'storeDocument']);
                        Route::post('upload', [DealDocumentController::class, 'uploadDocument']);
                        Route::put('update', [DealDocumentController::class, 'updateDocument']);
                        Route::delete('delete', [DealDocumentController::class, 'delete']);
                    });
                    Route::prefix('logs')->group(function () {
                        Route::get('', [DealLogController::class, 'loadLogs']);
                        Route::get('filters', [DealLogController::class, 'loadLogsFilters']);
                        Route::get('csv', [DealLogController::class, 'exportLogsToCsv']);
                    });
                });
            });
        });

        // Contracts
        Route::prefix('contracts')->group(function () {
            Route::get('get-payment-cost', [ContractController::class, 'getPaymentCost']);
            Route::get('template-csv', [ContractController::class, 'exportTemplateCsv']);
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('/', [ContractController::class, 'searchCompanyContracts']);
                Route::get('/products', [ContractController::class, 'searchCompanyContractProducts'])->name('');
                Route::get('first-load', [ContractController::class, 'firstLoad']);
                Route::get('fixed-rate-count', [ContractController::class, 'getFixedRateContractsCount']);
                Route::get('kpis', [ContractController::class, 'getKPIs']);
                Route::get('export-to-csv', [ContractController::class, 'exportToCsv']);
                Route::get('load-total-costs-for-period',
                    [ContractController::class, 'loadTotalCostsForPeriod']);
                Route::get('load-upcoming-renewals',
                    [ContractController::class, 'loadUpcomingRenewals']);
                Route::get('load-subscription-calendar-view',
                    [ContractController::class, 'loadSubscriptionCalendarView']);
                Route::get('load-subscription-list-view',
                    [ContractController::class, 'loadSubscriptionListView']);
                Route::get('load-import-csv-summary',
                    [ContractController::class, 'loadImportCSVSummary']);
                Route::post('store-multiple-from-csv',
                    [ContractController::class, 'storeMultipleFromCSV']);
                Route::post('store', [ContractController::class, 'store']);
                Route::put('update', [ContractController::class, 'update']);
                Route::delete('delete', [ContractController::class, 'delete']);
                Route::prefix('/product_contracts')->group(function () {
                    Route::post('store', [ContractController::class, 'storeProductContract']);
                    Route::put('update', [ContractController::class, 'updateProductContract']);
                });

                Route::prefix('/contract/{contract}')->where(['contract' => '[0-9]+'])->group(function () {
                    Route::get('', [ContractController::class, 'loadContract']);
                    Route::prefix('documents')->group(function () {
                        Route::get('', [ContractDocumentController::class, 'showAll']);
                        Route::post('store-link', [ContractDocumentController::class, 'storeLink']);
                        Route::post('store-document', [ContractDocumentController::class, 'storeDocument']);
                        Route::post('upload-document', [ContractDocumentController::class, 'uploadDocument']);
                        Route::put('update-link', [ContractDocumentController::class, 'updateLink']);
                        Route::put('update-document', [ContractDocumentController::class, 'updateDocument']);
                        Route::delete('delete', [ContractDocumentController::class, 'delete']);
                    });
                    Route::prefix('logs')->group(function () {
                        Route::get('', [ContractLogController::class, 'loadLogs']);
                        Route::get('filters', [ContractLogController::class, 'loadLogsFilters']);
                        Route::get('csv', [ContractLogController::class, 'exportLogsToCsv']);
                    });
                });
                Route::prefix('notifications')->group(function () {
                    Route::get('', [ContractNotificationController::class, 'showAll']);
                    Route::post('store', [ContractNotificationController::class, 'store']);
                    Route::put('update', [ContractNotificationController::class, 'update']);
                    Route::delete('delete', [ContractNotificationController::class, 'delete']);
                });
            });
        });

        // Reviews
        Route::prefix('review')->group(function () {
            Route::get('load-last-review', [ReviewController::class, 'loadLastReview']);
            Route::get('questions', [ReviewController::class, 'loadQuestions']);
            Route::get('approved', [ReviewController::class, 'countOfApproved']);
            Route::put('update', [ReviewController::class, 'update']);
            Route::delete('delete', [ReviewController::class, 'delete']);
            Route::post('pre-creation-check', [ReviewController::class, 'preCreationCheck']);
            Route::prefix('store')->group(function () {
                Route::post('step-one', [ReviewController::class, 'storeStepOne']);
                Route::post('step-two', [ReviewController::class, 'storeStepTwo']);
            });
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('all', [ReviewController::class, 'showForCompany']);
            });
            Route::prefix('{review}')->where(['review' => '[0-9]+'])->group(function () {
                Route::prefix('answer')->group(function () {
                    Route::get('', [ReviewAnswerController::class, 'showAll']);
                    Route::post('store', [ReviewAnswerController::class, 'store']);
                    Route::post('sync', [ReviewAnswerController::class, 'sync']);
                    Route::put('update', [ReviewAnswerController::class, 'update']);
                    Route::delete('delete', [ReviewAnswerController::class, 'delete']);
                });
                Route::prefix('reply')->group(function () {
                    Route::get('', [ReviewReplyController::class, 'showAll']);
                    Route::post('store', [ReviewReplyController::class, 'store']);
                    Route::put('update', [ReviewReplyController::class, 'update']);
                    Route::delete('delete', [ReviewReplyController::class, 'delete']);
                });
            });
        });

        // Bookmark
        Route::prefix('userproduct/bookmark')->group(function () {
            Route::get('', [UserProductBookmarkController::class, 'showAll']);
            Route::get('check', [UserProductBookmarkController::class, 'userByProductIdsBookmarkedStatus']);
            Route::get('{productId}', [UserProductBookmarkController::class, 'userByProductIdBookmarked']);
            Route::post('store', [UserProductBookmarkController::class, 'store']);
            Route::delete('delete', [UserProductBookmarkController::class, 'delete']);
        });

        // USER
        Route::prefix('user')->group(function () {
            Route::get('', [UserController::class, 'getUser']);
            Route::put('', [UserController::class, 'updateUser']);
            Route::get('i-still-active', [UserController::class, 'iStillActive']);
            Route::get('permissions', [UserController::class, 'getUserPermissions']);
            Route::get('companies', [UserController::class, 'getCompanies']);
            Route::get('search', [UserController::class, 'searchUser']);
            Route::prefix('{user}')->where(['user' => '[0-9]+'])->group(function () {
                Route::get('follow', [FollowUserController::class, 'followUser']);
                Route::prefix('reviews')->group(function () {
                    Route::get('by-user', [ReviewController::class, 'getReviewsByUser']);
                    Route::get('by-user-detailed', [ReviewController::class, 'getReviewsByUserDetailed']);
                });
                Route::get('send-confirm-email-code', [UserController::class, 'sendConfirmEmailCode'])
                    ->withoutMiddleware('verified');
                Route::get('verify-email-code', [UserController::class, 'verifyEmailCode'])
                    ->withoutMiddleware('verified');
                Route::get('stop-following', [FollowUserController::class, 'stopFollowingUser']);
                Route::prefix('notifications')->group(function () {
                    Route::get('', [UserNotificationController::class, 'showAll']);
                    Route::put('update-status', [UserNotificationController::class, 'updateStatus']);
                    Route::put('update-status-bulk', [UserNotificationController::class, 'updateStatusBulk']);
                });
                Route::prefix('my-stack')->group(function () {
                    Route::post('store', [MyStackUserController::class, 'store']);
                    Route::delete('delete', [MyStackUserController::class, 'delete']);
                });
                Route::prefix('profile')->group(function () {
                    Route::prefix('video')->group(function () {
                        Route::post('store', [UserProfileController::class, 'uploadVideo']);
                        Route::delete('delete', [UserProfileController::class, 'deleteVideo']);
                        Route::prefix('{video}')->where(['video' => '[0-9]+'])->group(function () {
                            Route::get('/', [UserProfileController::class, 'downloadVideo']);
                            Route::prefix('categories')->group(function () {
                                Route::post('/add', [UserProfileController::class, 'addVideoCategories']);
                                Route::post('/sync', [UserProfileController::class, 'syncVideoCategories']);
                                Route::delete('/delete', [UserProfileController::class, 'deleteVideoCategories']);
                            });
                        });
                    });
                });
            });
        });

        // 2fa
        Route::post('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'store'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.enable');

        Route::delete('/user/two-factor-authentication', [TwoFactorAuthenticationController::class, 'destroy'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.disable');

        Route::get('/user/two-factor-qr-code', [TwoFactorQrCodeController::class, 'show'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.qr-code');

        Route::get('/user/two-factor-url', [TwoFALoginController::class, 'showUrl'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.url');

        Route::get('/user/two-factor-secret-key', [TwoFactorSecretKeyController::class, 'show'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.secret-key');

        Route::get('/user/two-factor-recovery-codes', [RecoveryCodeController::class, 'index'])
            ->middleware($twoFactorMiddleware)
            ->name('two-factor.recovery-codes');

        Route::post('/user/two-factor-disable', [TwoFALoginController::class, 'twoFactorDisable']);

        // Feature Flag
        Route::prefix('feature-flag')->group(function () {
            Route::get('', [FeatureFlagController::class, 'showAll']);
            Route::post('store', [FeatureFlagController::class, 'store']);
            Route::put('update', [FeatureFlagController::class, 'update']);
            Route::delete('delete', [FeatureFlagController::class, 'delete']);
            Route::post('find-by-name', [FeatureFlagController::class, 'findByName']);
            Route::post('find-by-id', [FeatureFlagController::class, 'findById']);
        });

        // Pitch events
        Route::prefix('pitch-events')->group(function () {
            Route::get('/pitch-events-names', [PitchEventsController::class, 'showEventsSummary']);
            Route::post('/show-pitches-user-is-attending', [AttendanceController::class, 'showPitchesUserIsAttending']);
            Route::post('/store', [PitchEventsController::class, 'store']);
            Route::post('/update', [PitchEventsController::class, 'update']);
            Route::delete('/delete', [PitchEventsController::class, 'delete']);
            Route::post('/attend-the-event', [AttendanceController::class, 'attendTheEvent']);
            Route::post('/unsubscribe-the-event', [AttendanceController::class, 'unsubscribeTheEvent']);
            Route::post('/add-poll-question', [PitchEventsController::class, 'addPollQuestion']);
            Route::post('/get-active-poll-question', [PitchEventsController::class, 'getActivePollQuestion']);
            Route::prefix('{pitchEvent}')->where(['pitchEvent' => '[0-9]+'])->group(function () {
                Route::put('/end', [PitchEventsController::class, 'end']);
            });
            Route::prefix('moderator-broadcast-messages')->group(function () {
                Route::post('/', [BroadcastController::class, 'showAllModBroadMsgs']);
                Route::post('/store', [BroadcastController::class, 'storeModBroadMsg']);
            });
            Route::prefix('pitch-day')->group(function () {
                Route::post('/load-info', [PitchDayController::class, 'loadInfo']);
                Route::post('/load-moderator-info', [PitchDayController::class, 'loadModeratorInfo']);
                Route::post('/load-user-mod-msgs', [PitchDayController::class, 'loadUserModeratorMessages']);
                Route::post('/load-public-chat-msgs', [PitchDayController::class, 'loadPublicChatMessages']);
                Route::post('/send-message-to-moderator', [PitchDayController::class, 'sendMessageToModerator']);
                Route::post('/send-mod-message-to-user', [PitchDayController::class, 'sendModeratorMessageToUser']);
                Route::post('/send-message-to-public', [PitchDayController::class, 'sendMessageToPublic']);
                Route::post('/send-broadcast-message', [PitchDayController::class, 'sendBroadcastMessage']);
                Route::put('/update-broadcast-message', [PitchDayController::class, 'updateBroadcastMessage']);
                Route::post('/send-poll', [PitchDayController::class, 'sendPoll']);
            });
            Route::prefix('poll-answer')->group(function () {
                Route::post('/store', [PitchEventPollAnswerController::class, 'store']);
                Route::post('/download-csv-file', [PitchEventPollAnswerController::class, 'getCSVFile']);
            });
            Route::prefix('schedule-vendor')->group(function () {
                Route::post('/list-available', [VendorScheduleController::class, 'listPitchEventAvailableVendors']);
                Route::post('/store', [VendorScheduleController::class, 'scheduleVendorToPitchEvent']);
                Route::delete('/delete', [VendorScheduleController::class, 'removeScheduledVendorFromPitchEvent']);
                Route::put('/update', [VendorScheduleController::class, 'update']);
            });
        });

        // Polls
        Route::prefix('polls')->group(function () {
            Route::prefix('questions')->group(function () {
                Route::post('/detail', [PollQuestionController::class, 'detail']);
                Route::post('/store', [PollQuestionController::class, 'store']);
                Route::put('/update', [PollQuestionController::class, 'update']);
                Route::delete('/delete', [PollQuestionController::class, 'delete']);
                Route::post('/archive', [PollQuestionController::class, 'archivePollQuestion']);
                Route::get('/', [PollQuestionController::class, 'showAll']);
                Route::get('/active', [PollQuestionController::class, 'showAllActive']);
                Route::prefix('options')->group(function () {
                    Route::post('/store', [PollOptionController::class, 'store']);
                    Route::put('/update', [PollOptionController::class, 'update']);
                    Route::delete('/delete', [PollOptionController::class, 'delete']);
                    Route::post('/reorder', [PollOptionController::class, 'reorder']);
                    Route::post('/detail', [PollOptionController::class, 'detail']);
                    Route::post('/', [PollOptionController::class, 'showAll']);
                });
            });
        });

        // Vendor Site Seo
        Route::prefix('vendor-site-seo')->group(function () {
            Route::get('/profile/{vendor_site_seo_id}', [VendorSiteSeoController::class, 'profile'])
                ->where('vendor_site_seo_id', '[0-9]+');
            Route::post('/store', [VendorSiteSeoController::class, 'store']);
            Route::put('/update', [VendorSiteSeoController::class, 'update']);
            Route::delete('/delete', [VendorSiteSeoController::class, 'delete']);
            Route::get('/', [VendorSiteSeoController::class, 'showAll']);
            Route::prefix('social')->group(function () {
                Route::post('/store', [VendorSiteSeoSocialController::class, 'store']);
                Route::put('/update', [VendorSiteSeoSocialController::class, 'update']);
                Route::delete('/delete', [VendorSiteSeoSocialController::class, 'delete']);
                Route::get('/', [VendorSiteSeoSocialController::class, 'showAll']);
            });
        });

        // Partner page by subdomain
        Route::prefix('subdomain/{company:subdomain}')->group(function () {
            Route::get('', [PartnerPageController::class, 'loadBySubdomain']);
        });

        // Company
        Route::prefix('company')->group(function () {
            Route::post('add/bulk', [CompanyController::class, 'addCompaniesFromCSV']);
            Route::post('claim-profile', [CompanyClaimerController::class, 'addClaimer']);
            Route::post('claimers/responses/store', [CompanyClaimerResponseController::class, 'store']);
            Route::get('follow/{company}', [FollowCompanyController::class, 'followCompany'])
                ->where('company', '[0-9]+');
            Route::get('stop-following/{company}', [FollowCompanyController::class, 'stopFollowingCompany'])
                ->where('company', '[0-9]+');
            Route::post('store', [CompanyController::class, 'store']);
            Route::put('update-banner-flag', [CompanyController::class, 'updateBannerFlag']);
            Route::put('update-hide-expenses', [CompanyController::class, 'updateHideExpenses']);
            Route::put('update-affiliate-popup', [CompanyController::class, 'updateAffiliatePopup']);
            Route::delete('delete', [CompanyController::class, 'delete']);
            Route::get('', [CompanyController::class, 'showAll']);
            Route::prefix('invite')->group(function () {
                Route::get('users-multiple-roles/template-csv', [CompanyInviteController::class, 'exportTemplateCSV']);
            });
            Route::prefix('{company:friendly_url}')->group(function () {
                Route::get('claimers', [CompanyController::class, 'showClaimers']);
                Route::get('roles', [CompanyController::class, 'showRoles']);
                Route::prefix('affiliates')->group(function () {
                    Route::get('', [CompanyAffiliateController::class, 'showAll']);
                    Route::get('filters', [CompanyAffiliateController::class, 'showAllFilters']);
                    Route::get('generate-csv', [CompanyAffiliateController::class, 'generateCSV']);
                    Route::get('invites', [CompanyAffiliateController::class, 'showAllInvites']);
                    Route::post('invite/resendInviteAllAffiliate', [CompanyAffiliateController::class, 'resendInviteAllAffiliate']);
                    Route::post('invite/resendBulkInviteAffiliate', [CompanyAffiliateController::class, 'resendBulkInviteAffiliate']);
                    Route::post('invite/csv', [CompanyAffiliateController::class, 'addAffiliatesInvitationsFromCSV']);
                    Route::get('brand-stack-adoption-detail',
                        [CompanyAffiliateController::class, 'brandStackAdoptionDetail']);
                    Route::get('calculate-brand-stack-adoption',
                        [CompanyAffiliateController::class, 'calculateBrandStackAdoption']);
                    Route::post('update-brand', [CompanyAffiliateController::class, 'createOrUpdateBrand']);
                    Route::delete('delete', [CompanyAffiliateController::class, 'deleteAffiliate']);
                });
                Route::prefix('emails')->group(function () {
                    Route::get('', [CustomizableEmailController::class, 'showAll']);
                    Route::prefix('{email}')->where(['email' => '[0-9]+'])->group(function () {
                        Route::put('update', [CustomizableEmailController::class, 'updateOrCreate']);
                        Route::delete('delete', [CustomizableEmailController::class, 'delete']);
                    });
                });
                Route::prefix('invite')->group(function () {
                    Route::get('', [CompanyInviteController::class, 'showAllInvites']);
                    Route::post('', [CompanyInviteController::class, 'inviteClients']);
                    Route::get('showPendingCustomersInvites', [CompanyInviteController::class, 'showPendingCustomersInvites']);
                    Route::get('showAllPendingUsersFilters', [CompanyInviteController::class, 'showAllPendingUsersFilters']);
                    Route::get('showAllPendingCustomerFilters', [CompanyInviteController::class, 'showAllPendingCustomerFilters']);
                    Route::get('open-invitation', [CompanyInviteController::class, 'getOpenInvitation']);
                    Route::post('users', [CompanyInviteController::class, 'inviteUsers']);
                    Route::post('users-multiple-roles/store', [CompanyInviteController::class, 'inviteUsersMultipleRoles']);
                    Route::post('users-multiple-roles/validate-csv', [CompanyInviteController::class, 'validateInvitesCSV']);
                    Route::post('user/resend/{invite}', [CompanyInviteController::class, 'resendInviteUser']);
                    Route::post('user/resendInviteAllUser', [CompanyInviteController::class, 'resendInviteAllUser']);
                    Route::post('user/resendBulkInviteUser', [CompanyInviteController::class, 'resendBulkInviteUser']);
                    Route::post('affiliate/resend/{invite}', [CompanyInviteController::class, 'resendInviteAffiliate']);
                    Route::post('resendBulkInviteAffiliate', [CompanyInviteController::class, 'resendBulkInviteAffiliate']);
                    Route::post('affiliate/resendInviteAllAffiliate', [CompanyInviteController::class, 'resendInviteAllAffiliate']);
                    Route::post('affiliates', [CompanyInviteController::class, 'inviteAffiliates']);
                    Route::delete('delete', [CompanyInviteController::class, 'deleteInvite']);
                });
                Route::prefix('clients')->group(function () {
                    Route::get('', [CompanyClientController::class, 'showAll']);
                    Route::get('count', [CompanyClientController::class, 'getCustomerCountForCompany']);
                    Route::get('stack', [CompanyClientController::class, 'showClientsStack']);
                    Route::get('filters', [CompanyClientController::class, 'showAllFilters']);
                    Route::get('sub-domain', [CompanyClientController::class, 'generateClientSubdomain']);
                    Route::get('summary', [CompanyClientController::class, 'clientsSummary']);
                    Route::get('contracts', [CompanyClientController::class, 'showClientContracts']);
                    Route::post('store', [CompanyClientController::class, 'store']);
                    Route::put('update', [CompanyClientController::class, 'update']);
                    Route::delete('delete', [CompanyClientController::class, 'delete']);
                    Route::post('add/csv', [CompanyClientController::class, 'addClientsFromCSV']);
                    Route::get('generate-csv', [CompanyClientController::class, 'generateCSV']);
                    Route::get('download-pdf', [CompanyClientController::class, 'downloadPDF']);
                });
                Route::prefix('marketplace')->group(function () {
                    Route::post('store', [CompanyMarketplacePartnerController::class, 'store']);
                    Route::put('update', [CompanyMarketplacePartnerController::class, 'update']);
                    Route::delete('delete', [CompanyMarketplacePartnerController::class, 'delete']);
                    Route::post('add/csv', [CompanyMarketplacePartnerController::class, 'addPartnersFromCSV']);
                    Route::post('add/csv/status', [CompanyMarketplacePartnerController::class, 'getCSVProcessingStatus']);
                    Route::post('add/bulk', [CompanyMarketplacePartnerController::class, 'addPartners']);
                });
            });
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::prefix('dashboard')->group(function () {
                    Route::get('feature-counts', [CompanyDashboardController::class, 'featureCounts'])->name('api.company.dashboard.feature-counts');
                    Route::get('quick-overview', [CompanyDashboardController::class, 'quickOverviewData'])->name('api.company.dashboard.quick-overview');
                    Route::get('stack-breakdown', [CompanyDashboardController::class, 'stackBreakdown'])->name('api.company.dashboard.stack-breakdown');
                    Route::get('contract-timeline', [CompanyDashboardController::class, 'contractTimeline'])->name('api.company.dashboard.contract-timeline');
                    Route::get('export-stack-breakdown', [CompanyDashboardController::class, 'exportStackBreakdown'])->name('api.company.dashboard.export-stack-breakdown');
                    Route::get('category-detail-count', [CompanyDashboardController::class, 'categoryDetailCount'])->name('api.company.dashboard.category-detail-count');
                });
                Route::put('update', [CompanyController::class, 'update']);
                Route::get('users', [CompanyController::class, 'showUsers']);
                Route::prefix('users')->group(function () {
                    Route::get('filters', [CompanyController::class, 'showFiltersForUsers']);
                    Route::prefix('requests')->group(function () {
                        Route::get('options', [UserCompanyRequestController::class, 'showUserRequestRejectionOptions']);
                        Route::post('search', [UserCompanyRequestController::class, 'searchRequests']);
                        Route::post('approve', [UserCompanyRequestController::class, 'approve']);
                        Route::post('decline', [UserCompanyRequestController::class, 'decline']);
                    });
                });
                Route::prefix('user-management')->group(function () {
                    Route::get('filters', [CompanyUserController::class, 'showFilters']);
                    Route::get('', [CompanyUserController::class, 'showUsers']);
                    Route::get('counts', [CompanyUserController::class, 'loadCounts']);
                });
                // Whitelabeling
                Route::prefix('whitelabeling')->group(function () {
                    Route::get('', [CompanyWhitelabelingController::class, 'loadByCompanyId']);
                    Route::put('update', [CompanyWhitelabelingController::class, 'update']);
                    Route::post('upload-image', [CompanyWhitelabelingController::class, 'uploadImage']);
                });
                // Social
                Route::prefix('social')->group(function () {
                    Route::post('sync', [CompanySocialController::class, 'sync']);
                    Route::post('store', [CompanySocialController::class, 'store']);
                    Route::put('update', [CompanySocialController::class, 'update']);
                    Route::delete('delete', [CompanySocialController::class, 'delete']);
                });
                // Social
                Route::prefix('config')->group(function () {
                    Route::get('', [CompanyConfigurationController::class, 'showAll']);
                    Route::put('update', [CompanyConfigurationController::class, 'update']);
                });
                Route::get('subscription', [CompanyController::class, 'subscription']);
                Route::get('partner-page', [PartnerPageController::class, 'loadByCompanyId']);
                Route::prefix('category')->group(function () {
                    Route::post('/store', [CompanyController::class, 'storeCategory']);
                    Route::delete('/delete/{category}', [CompanyController::class, 'deleteCategory'])
                        ->where('category', '[0-9]+');
                });
                Route::prefix('image')->group(function () {
                    Route::post('store', [VendorProfileImageController::class, 'upload']);
                    Route::delete('delete/{image_id}', [VendorProfileImageController::class, 'delete'])
                        ->where('image_id', '[0-9]+');
                });
                Route::prefix('product')->group(function () {
                    Route::post('/store', [ProductController::class, 'store']);
                    Route::put('/update', [ProductController::class, 'update']);
                    Route::delete('/delete/{product}', [ProductController::class, 'delete'])
                        ->where('product', '[0-9]+');
                    Route::prefix('{product}')->where(['product' => '[0-9]+'])->group(function () {
                        Route::prefix('image')->group(function () {
                            Route::post('upload', [ProductController::class, 'uploadImage']);
                            Route::delete('delete', [ProductController::class, 'deleteImage']);
                        });
                        Route::prefix('video')->group(function () {
                            Route::post('upload', [ProductController::class, 'uploadVideo']);
                            Route::delete('delete', [ProductController::class, 'deleteVideo']);
                        });
                        Route::prefix('features')->group(function () {
                            Route::post('store', [ProductController::class, 'storeProductFeature']);
                            Route::get('{product_feature}', [ProductController::class, 'viewProductFeature']);
                            Route::put('{product_feature}', [ProductController::class, 'updateProductFeature']);
                            Route::delete('{product_feature}', [ProductController::class, 'deleteProductFeature']);
                        });
                        Route::prefix('pricing')->group(function () {
                            Route::get('/', [ProductController::class, 'showAllProductPricing']);
                            Route::post('store', [ProductController::class, 'storeProductPricing']);
                            Route::get('{product_pricing}', [ProductController::class, 'viewProductPricing']);
                            Route::put('{product_pricing}', [ProductController::class, 'updateProductPricing']);
                            Route::delete('{product_pricing}', [ProductController::class, 'deleteProductPricing']);
                        });
                    });
                });
                Route::prefix('notification-recipient')->group(function () {
                    Route::post('/store', [CompanyNotificationRecipientsController::class, 'store']);
                    Route::put('/update', [CompanyNotificationRecipientsController::class, 'update']);
                    Route::delete('/delete', [CompanyNotificationRecipientsController::class, 'delete']);
                    Route::get('/{notification_recipient}', [CompanyNotificationRecipientsController::class, 'showById'])
                        ->where('notification_recipient', '[0-9]+');
                    Route::get('/', [CompanyNotificationRecipientsController::class, 'showAll']);
                });
                Route::prefix('my-stack')->group(function () {
                    Route::post('store', [MyStackCompanyController::class, 'store']);
                    Route::prefix('update')->group(function () {
                        Route::put('', [MyStackCompanyController::class, 'update']);
                        Route::put('partner-status', [MyStackCompanyController::class, 'updatePartnerStatus']);
                    });
                    Route::delete('delete', [MyStackCompanyController::class, 'delete']);
                    Route::delete('delete-category', [MyStackCompanyController::class, 'deleteCategory']);
                    Route::get('available-vendors', [MyStackCompanyController::class, 'availableVendors']);
                    Route::get('generate-csv', [MyStackCompanyController::class, 'generateCSV']);
                    Route::get('download-pdf', [MyStackCompanyController::class, 'downloadPDF']);

                    Route::prefix('{myStack}')->group(function () {
                        Route::get('client-usage', [MyStackCompanyController::class, 'showClientsUsingStack']);
                        Route::post('client-usage', [MyStackCompanyController::class, 'addClientUsage']);
                        Route::delete('client-usage', [MyStackCompanyController::class, 'deleteClientUsage']);
                    });
                });
                Route::prefix('reviews')->group(function () {
                    Route::get('export-csv', [ReviewController::class, 'exportReviews']);
                });
                Route::prefix('company-support-schedule')->group(function () {
                    Route::get('/', [CompanySupportScheduleController::class, 'getVendorHours']);
                    Route::post('/add-update', [CompanySupportScheduleController::class, 'addUpdateHours']);
                });
                Route::prefix('missing-request')->group(function () {
                    Route::post('store', [MissingProductOrVendorController::class, 'store']);
                    Route::post('store-multiple', [MissingProductOrVendorController::class, 'storeMultiple']);
                });
            });
        });

        // Customers
        Route::prefix('customer')->group(function () {
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('vendors', [ClientVendorController::class, 'showAll']);
                Route::get('products', [ClientProductController::class, 'showAll']);
                Route::prefix('my-stack')->group(function () {
                    Route::get('', [MyStackCustomerController::class, 'showAll']);
                    Route::get('categories-filled', [MyStackCustomerController::class, 'showAllCategoriesFilled']);
                    Route::post('store', [MyStackCustomerController::class, 'store']);
                    Route::prefix('update')->group(function () {
                        Route::put('', [MyStackCustomerController::class, 'update']);
                        Route::put('partner-status', [MyStackCustomerController::class, 'updatePartnerStatus']);
                    });
                    Route::delete('delete', [MyStackCustomerController::class, 'delete']);
                    Route::get('generate-csv', [MyStackCustomerController::class, 'generateCSV']);
                    Route::get('generate-pdf', [MyStackCustomerController::class, 'generatePDF']);
                });
            });
        });

        // Chat routes
        Route::prefix('chat')->group(function () {
            Route::prefix('one-to-one')->group(function () {
                Route::get('', [OneToOneChatController::class, 'load']);
                Route::get('list/{company}', [OneToOneChatController::class, 'list'])
                    ->where(['company' => '[0-9]+']);
                Route::post('send', [OneToOneChatController::class, 'send']);
                Route::post('send-message-to-msps', [OneToOneChatController::class, 'sendMessageToMsps']);
                Route::post('send-message-to-contact-list', [OneToOneChatController::class, 'sendMessageToContactList']);
                Route::post('toggle-visibility', [OneToOneChatController::class, 'toggleVisibility']);
            });
        });

        // Partner routes
        Route::prefix('partner')->group(function () {
            Route::get('invitation/{inviteId}/accept', [InvitePartnerController::class, 'acceptInvite'])
                ->where(['inviteId' => '[A-Za-z0-9]+']);
            Route::put('invitation/{vendor}/accept-multiple', [InvitePartnerController::class, 'acceptMultipleInvite'])
                ->where(['vendor' => '[0-9]+']);
            Route::put(
                'invitation/{inviteId}/update-partnership',
                [InvitePartnerController::class, 'updatePartnership']
            )->where(['inviteId' => '[0-9]+']);
            Route::post('remove-invite', [InvitePartnerController::class, 'removeInviteByIds']);
            Route::prefix('{vendor}')->where(['vendor' => '[0-9]+'])->group(function () {
                Route::get('invites', [InvitePartnerController::class, 'getInvites']);
                Route::prefix('open-invitation')->group(function () {
                    Route::get('', [InvitePartnerController::class, 'getOpenInvitation']);
                });
                Route::prefix('invite')->group(function () {
                    Route::get('filters', [InvitePartnerController::class, 'filter']);
                    Route::post('', [InvitePartnerController::class, 'sendInvite']);
                    Route::post('resend-invite/{invite}', [InvitePartnerController::class, 'reSendInvite']);
                    Route::post('resendAllInvite', [InvitePartnerController::class, 'resendAllInvite']);
                    Route::post('resendBulkInvite', [InvitePartnerController::class, 'resendBulkInvite']);
                    Route::post('csv', [InvitePartnerController::class, 'sendInviteFromCsv']);
                });
                Route::post('remove-invite', [InvitePartnerController::class, 'removeInvite']);
                route::prefix('customer')->group(function () {
                    Route::get('', [PartnerCustomerController::class, 'getAllCustomer']);
                    Route::get('filters', [PartnerCustomerController::class, 'filters']);
                    Route::put('is-ignored', [PartnerCustomerController::class, 'isIgnored']);
                });
                Route::get('get-pending-requests-invitation-sync-customer-counts', [
                    PartnerCustomerController::class, 'getPendingRequestsInvitationSyncCustomerCount',
                ]
                );
            });
            Route::prefix('page')->group(function () {
                Route::get('get-company/{company:subdomain}', [PartnerPageController::class, 'getCompany']);
                Route::put('update', [PartnerPageController::class, 'update']);
                Route::prefix('header-image')->group(function () {
                    Route::post('update', [PartnerPageController::class, 'updateHeaderImage']);
                    Route::delete('delete', [PartnerPageController::class, 'deleteHeaderImage']);
                });
                Route::prefix('{partnerAsset}')->group(function () {
                    Route::prefix('asset')->group(function () {
                        Route::delete('delete', [PartnerAssetsController::class, 'delete']);
                        Route::prefix('{partnerMedia}')->where(['partnerMedia' => '[0-9]+'])->group(function () {
                            Route::delete('delete', [PartnerAssetsController::class, 'deleteMedia']);
                        });
                    });
                });
                Route::prefix('{owner}')->where(['owner' => '[0-9]+'])->group(function () {
                    Route::prefix('asset')->group(function () {
                        Route::get('by-msp', [PartnerAssetsController::class, 'showAssets']);
                    });
                    Route::prefix('template')->group(function () {
                        Route::get('', [PartnerTemplateController::class, 'showAllSavedTemplates']);
                        Route::post('store', [PartnerTemplateController::class, 'store']);
                        Route::get('count', [PartnerTemplateController::class, 'getAllSavedTemplatesCount']);
                        Route::prefix('saved-by-msp')->group(function () {
                            Route::get('', [PartnerTemplateController::class, 'showSavedTemplates']);
                            Route::get('{media}', [PartnerTemplateController::class, 'getBase64MEDIA'])
                                ->where(['media' => '[0-9]+']);
                        });
                    });
                });
                Route::prefix('{partnerTemplate}')->where(['partnerTemplate' => '[0-9]+'])->group(function () {
                    Route::prefix('template')->group(function () {
                        Route::delete('delete', [PartnerTemplateController::class, 'delete']);
                        Route::post('update', [PartnerTemplateController::class, 'update']);
                    });
                });
                Route::prefix('{partnerPage}')->where(['partnerPage' => '[0-9]+'])->group(function () {
                    Route::get('filters', [PartnerPageFilterController::class, 'filters']);
                    Route::prefix('search')->group(function () {
                        Route::get('blog', [PartnerPageSectionContentController::class, 'searchBlog']);
                        Route::get('assets', [PartnerPageSectionContentController::class, 'searchAssets']);
                        Route::get('document', [PartnerPageSectionContentController::class, 'searchDocument']);
                        Route::get('template', [PartnerPageSectionContentController::class, 'searchTemplate']);
                        Route::get('video', [PartnerPageSectionContentController::class, 'searchVideo']);
                    });
                    Route::prefix('asset')->group(function () {
                        Route::post('store', [PartnerAssetsController::class, 'store']);
                    });
                    Route::prefix('engagement')->group(function () {
                        Route::get('type', [PartnerPageEngagementController::class, 'loadTypeData']);
                        Route::get('partner', [PartnerPageEngagementController::class, 'loadPartnerData']);
                        Route::get('content', [PartnerPageEngagementController::class, 'loadContentData']);
                        Route::get('change-over-time', [PartnerPageEngagementController::class, 'loadChangeOverTimeData']);
                    });
                    Route::prefix('section')->group(function () {
                        Route::get('', [PartnerPageSectionController::class, 'showAll']);
                        Route::post('store', [PartnerPageSectionController::class, 'store']);
                        Route::put('update', [PartnerPageSectionController::class, 'update']);
                        Route::delete('delete', [PartnerPageSectionController::class, 'delete']);
                        Route::prefix('content')->group(function () {
                            Route::get('', [PartnerPageSectionContentController::class, 'showAll']);
                            Route::post('store', [PartnerPageSectionContentController::class, 'store']);
                            Route::put('toggle-hide', [PartnerPageSectionContentController::class, 'toggleHide']);
                            Route::delete('delete', [PartnerPageSectionContentController::class, 'delete']);
                        });
                    });
                });
                Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                    Route::prefix('template/filters')->group(function () {
                        Route::get('', [PartnerPageFilterController::class, 'templateFilter']);
                    });
                });
            });
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::prefix('brandable-contact-info')->group(function () {
                    Route::get('', [PartnerBrandableContactInfoController::class, 'showAll']);
                    Route::post('store', [PartnerBrandableContactInfoController::class, 'store']);
                    Route::put('update', [PartnerBrandableContactInfoController::class, 'update']);
                    Route::delete('delete', [PartnerBrandableContactInfoController::class, 'delete']);
                });
            });
        });

        Route::prefix('partners')->group(function () {
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('', [CompanyPartnerController::class, 'showAll']);
                Route::get('users', [CompanyPartnerController::class, 'users']);
                Route::get('partners-with-contacts', [CompanyPartnerController::class, 'partnersWithContacts']);
                Route::get('partner-user-counts', [CompanyPartnerController::class, 'partnerUserCounts']);
                Route::get('activities', [CompanyPartnerController::class, 'showActivityOfPartners']);
                Route::get('recommended-vendors', [CompanyPartnerController::class, 'showRecommendedVendors']);
                Route::get('industry-events', [CompanyPartnerController::class, 'showIndustryEvents']);
                Route::get('product-reviews-and-pending-requests-counts', [
                    CompanyPartnerController::class, 'productReviewsAndPendingRequestsCount',
                ]
                );
                Route::get('download', [CompanyPartnerController::class, 'downloadPartnerReport']);
            });
            Route::get('filters', [PartnersFilterController::class, 'filters']);
        });

        Route::prefix('contact_lists')->group(function () {
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('', [CompanyContactListController::class, 'showAll']);
                Route::post('store', [CompanyContactListController::class, 'store']);
                Route::post('show', [CompanyContactListController::class, 'show']);
                Route::post('items', [CompanyContactListController::class, 'contactListItems']);
                Route::put('update', [CompanyContactListController::class, 'update']);
                Route::delete('delete', [CompanyContactListController::class, 'delete']);
            });
        });

        // Vendor Onboarding
        Route::prefix('vendor-onboarding')->group(function () {
            Route::put('/add-category', [VendorOnboardingController::class, 'addCategory']);
            Route::put('/update', [VendorOnboardingController::class, 'updateProfile']);
            Route::post('/claim-profile', [VendorOnboardingController::class, 'claimProfile']);
        });

        // Profiles
        Route::prefix('/profiles/')->group(function () {
            Route::prefix('/user/{profileUser}')->where(['profileUser' => '[0-9]+'])->group(function () {
                Route::post('/document/store', [UserProfileController::class, 'uploadDocument']);
                Route::delete('/document/delete', [UserProfileController::class, 'deleteDocument'])->where(['document' => '[0-9]+']);
                Route::prefix('/image')->group(function () {
                    Route::post('/store', [UserProfileController::class, 'uploadImage']);
                    Route::delete('/delete/{image}', [UserProfileController::class, 'deleteProfileImage'])->where(['image' => '[0-9]+']);
                });
            });
            Route::prefix('vendor-basic/{companyId}')->where(['companyId' => '[0-9]+'])->group(function () {
                Route::prefix('contact-info')->group(function () {
                    Route::post('store', [VendorProfileContactInfoController::class, 'store']);
                    Route::put('update', [VendorProfileContactInfoController::class, 'update']);
                    Route::delete('delete', [VendorProfileContactInfoController::class, 'delete']);
                });
                Route::prefix('contacts')->group(function () {
                    Route::post('store', [VendorProfileContactsController::class, 'store']);
                    Route::put('update', [VendorProfileContactsController::class, 'update']);
                    Route::delete('delete', [VendorProfileContactsController::class, 'delete']);
                });
                Route::prefix('/overview')->group(function () {
                    Route::post('/sync-languages', [VendorProfileOverviewController::class, 'syncLanguages']);
                    Route::post('/sync-countries', [VendorProfileOverviewController::class, 'syncCountries']);
                });
                Route::prefix('video')->group(function () {
                    Route::post('store', [VendorProfileVideoController::class, 'uploadVideo']);
                    Route::delete('delete', [VendorProfileVideoController::class, 'deleteVideo']);
                    Route::prefix('{video}')->where(['video' => '[0-9]+'])->group(function () {
                        Route::put('update', [VendorProfileVideoController::class, 'updateVideo']);
                        Route::prefix('categories')->group(function () {
                            Route::post('add', [VendorProfileVideoController::class, 'addVideoCategories']);
                            Route::post('sync', [VendorProfileVideoController::class, 'syncVideoCategories']);
                            Route::delete('delete', [VendorProfileVideoController::class, 'deleteVideoCategories']);
                        });
                    });
                });
                Route::prefix('document')->group(function () {
                    Route::post('store', [VendorProfileDocumentController::class, 'uploadDocument']);
                    Route::put('{document}/update', [VendorProfileDocumentController::class, 'updateDocument'])
                        ->where('document', '[0-9]+');
                    Route::delete('delete', [VendorProfileDocumentController::class, 'deleteDocument']);
                });
                Route::prefix('brandable')->group(function () {
                    Route::post('store', [VendorProfileBrandableController::class, 'uploadDocument']);
                    Route::post('{brandable}/update', [VendorProfileBrandableController::class, 'updateDocument'])
                        ->where('brandable', '[0-9]+');
                    Route::delete('delete', [VendorProfileBrandableController::class, 'deleteDocument']);
                });
                Route::prefix('image')->group(function () {
                    Route::post('store', [VendorProfileImageController::class, 'upload']);
                    Route::delete('delete/{image_id}', [VendorProfileImageController::class, 'delete'])
                        ->where('image_id', '[0-9]+');
                });
                Route::prefix('/people')->group(function () {
                    Route::post('/toggle-user-visibility', [VendorProfilePeopleController::class, 'toggleUserVisibility']);
                });
            });

            // MSP Profile
            Route::prefix('msp')->group(function () {
                Route::prefix('{company:friendly_url}')->group(function () {
                    Route::get('/', [MspProfileController::class, 'getProfile']);
                    Route::prefix('contact-info')->group(function () {
                        Route::get('', [MspProfileContactInfoController::class, 'contactInfo']);
                        Route::post('store', [MspProfileContactInfoController::class, 'store']);
                        Route::put('update', [MspProfileContactInfoController::class, 'update']);
                        Route::delete('/delete', [MspProfileContactInfoController::class, 'delete']);
                    });
                });
            });
        });

        // Profile enrichment
        Route::prefix('/profile-enrichment')->group(function () {
            Route::get('/questions', [ProfileEnrichmentController::class, 'loadQuestions']);
            Route::prefix('/company/{friendly_url}')->group(function () {
                Route::prefix('answers')->group(function () {
                    Route::post('/store', [ProfileEnrichmentCompanyAnswersController::class, 'store']);
                    Route::put('/update', [ProfileEnrichmentCompanyAnswersController::class, 'update']);
                    Route::delete('/delete', [ProfileEnrichmentCompanyAnswersController::class, 'delete']);
                });
            });
        });

        // Media
        Route::prefix('media')->group(function () {
            Route::post('image/{media}/update', [MediaController::class, 'updateImageProperties'])->where(['media' => '[0-9]+']);
            Route::post('document/{media}/update', [MediaController::class, 'updateDocumentProperties'])->where(['media' => '[0-9]+']);
            Route::post('video/{media}/update', [MediaController::class, 'updateVideoProperties'])->where(['media' => '[0-9]+']);
            Route::post('update/{media}', [MediaController::class, 'update'])->where(['media' => '[0-9]+']);
            Route::delete('delete/{media}', [MediaController::class, 'delete'])->where(['media' => '[0-9]+']);
        });

        // Media Galleries
        Route::prefix('media-gallery')->group(function () {
            Route::post('store', [MediaGalleryController::class, 'store']);
            Route::prefix('{mediaGallery}')->where(['mediaGallery' => '[0-9]+'])->group(function () {
                Route::delete('delete', [MediaGalleryController::class, 'delete']);
                Route::post('add-medias', [MediaGalleryController::class, 'addMedias']);
                Route::post('edit-properties', [MediaGalleryController::class, 'editMediaGalleryProperties']);
                Route::prefix('{media}')->group(function () {
                    Route::delete('delete', [MediaGalleryController::class, 'deleteMedia']);
                    Route::get('download', [MediaGalleryController::class, 'downloadImage']);
                    Route::post('edit-properties', [MediaGalleryController::class, 'editMediaProperties']);
                });
            });
        });

        // Industry events
        Route::prefix('industry-events')->group(function () {
            Route::get('show-all', [IndustryEventsController::class, 'showAll']);
            Route::post('store', [IndustryEventsController::class, 'store']);
            Route::post('update', [IndustryEventsController::class, 'update']);
            Route::delete('delete', [IndustryEventsController::class, 'delete']);
        });

        // Shout Out
        Route::prefix('shout-out')->group(function () {
            Route::post('/store', [ShoutOutController::class, 'store']);
            Route::put('/update', [ShoutOutController::class, 'update']);
            Route::delete('/delete', [ShoutOutController::class, 'delete']);
        });

        // Blog
        Route::prefix('blog')->group(function () {
            Route::post('/store', [BlogController::class, 'store']);
            Route::put('/update', [BlogController::class, 'update']);
            Route::delete('/delete', [BlogController::class, 'delete']);
            Route::prefix('image')->group(function () {
                Route::delete('/delete', [BlogController::class, 'deleteImage']);
                Route::prefix('upload')->group(function () {
                    Route::post('/header', [BlogController::class, 'uploadHeaderImage']);
                    Route::post('/content', [BlogController::class, 'uploadContentImage']);
                });
            });
        });

        // Focus
        Route::prefix('focus')->group(function () {
            Route::prefix('/company/{companyId}')->where(['companyId' => '[0-9]+'])->group(function () {
                Route::post('/set-percentages', [FocusController::class, 'savePercentages']);
            });
        });

        // Newsfeed
        Route::prefix('news-feed')->group(function () {
            Route::prefix('/user')->group(function () {
                Route::get('/', [NewsFeedController::class, 'getUserNewsFeed']);
            });
        });

        // Analytics
        Route::prefix('analytics')->group(function () {
            Route::get('profiles-who-liked', [AnalyticsController::class, 'profilesWhoLiked']);
        });

        // Logged in user avatars
        Route::get('/user-avatars', [AuthenticatedSessionController::class, 'getUserAvatars']);

        // Comments
        Route::prefix('comments')->group(function () {
            Route::post('store', [CommentController::class, 'store']);
            Route::put('update', [CommentController::class, 'update']);
            Route::post('update-image', [CommentController::class, 'updateImage']);
            Route::delete('delete', [CommentController::class, 'delete']);
            Route::put('flag', [CommentController::class, 'flag']);
        });
        Route::prefix('social-media')->group(function () {
            Route::get('', [SocialMediaController::class, 'showAll']);
        });

        // Files management /*1*/
        Route::prefix('files')->group(function () {
            Route::middleware(['bulk-upload'])->prefix('{company}')->group(function () {
                Route::post('store-chunk', [FileUploadController::class, 'uploadChunkFile']);
                Route::post('cancel-upload', [FileUploadController::class, 'cancelUpload']);
                Route::post('send-summary', [FileUploadController::class, 'sendSummaryToUser']);
            });
        });

        // Folder Management
        Route::prefix('folder')->group(function () {
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('', [FolderController::class, 'showAll']);
                Route::get('filters', [FolderController::class, 'foldersFilters']);
                Route::post('store', [FolderController::class, 'store']);
                Route::put('update', [FolderController::class, 'update']);
                Route::delete('delete', [FolderController::class, 'delete']);
                Route::post('reorder', [FolderController::class, 'reorder']);
                Route::prefix('content')->group(function () {
                    Route::get('', [FolderContentController::class, 'showAll']);
                    Route::post('store', [FolderContentController::class, 'store']);
                    Route::delete('delete', [FolderContentController::class, 'delete']);
                    Route::post('store-multiple', [FolderContentController::class, 'storeMultipleFiles']);
                    Route::get('download', [FolderContentController::class, 'download']);
                    Route::put('update-status', [FolderContentController::class, 'updateStatus']);
                });
            });
            Route::prefix('{folder}')->where(['folder' => '[0-9]+'])->group(function () {
                Route::prefix('content')->group(function () {
                    Route::get('filters', [FolderContentController::class, 'folderContentsFilters']);
                });
            });
        });
        // Refer
        Route::prefix('refer')->group(function () {
            Route::get('get-draft-message', [ReferController::class, 'getDraftMessage']);
            Route::post('send-email', [ReferController::class, 'sendEmail']);
        });

        // Bulletin
        Route::prefix('bulletin')->group(function () {
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::get('', [BulletinController::class, 'showAll']);
                Route::post('store', [BulletinController::class, 'store']);
                Route::put('update', [BulletinController::class, 'update']);
                Route::put('inactive', [BulletinController::class, 'inactive']);
                Route::prefix('delete')->group(function () {
                    Route::delete('', [BulletinController::class, 'delete']);
                    Route::delete('multiple', [BulletinController::class, 'deleteMultiple']);
                });
                Route::get('filters', [BulletinFilterController::class, 'filters']);
            });
        });

        // Plaid
        Route::prefix('plaid')->group(function () {
            Route::get('link-token', [PlaidController::class, 'getLinkToken'])->name('api.plaid.get-link-token');
            Route::post('exchange-public-token', [PlaidController::class, 'exchangeToken'])->name('api.plaid.exchange-public-token');
            Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
                Route::prefix('alert')->group(function () {
                    Route::post('settings', [PlaidCompanyAlertController::class, 'storeAlertSettings'])->name('api.plaid.company.alert.settings.post');
                    Route::get('settings', [PlaidCompanyAlertController::class, 'getAlertSettings'])->name('api.plaid.company.alert.settings.get');
                    Route::get('notifications', [PlaidCompanyAlertController::class, 'showAllAlertNotifications'])->name('api.plaid.company.alert.show-all-notifications.get');
                });
                Route::get('linked-accounts', [PlaidCompanyController::class, 'showAllLinkedAccounts'])->name('api.plaid.company.linked-accounts');
                Route::get('count', [PlaidCompanyController::class, 'count'])->name('api.plaid.company.count');
                Route::get('transactions', [PlaidCompanyController::class, 'showAllTransactions'])->name('api.plaid.company.show-all-transactions');
                Route::put('transaction', [PlaidCompanyController::class, 'updateTransaction'])->name('api.plaid.company.transaction.update');
                Route::prefix('bulk')->group(function () {
                    Route::post('transaction', [PlaidCompanyController::class, 'bulkUpdateTransaction'])->name('api.plaid.company.bulk.update.transaction');
                    Route::post('subscription', [PlaidCompanyController::class, 'bulkUpdateSubscription'])->name('api.plaid.company.bulk.update.subscription');
                    Route::post('sync-bank-account', [PlaidCompanyController::class, 'bulkSyncBankAccount'])->name('api.plaid.company.bulk.sync-bank-account');
                    Route::delete('bank-account', [PlaidCompanyController::class, 'bulkDeleteBankAccount'])->name('api.plaid.company.bulk.delete.bank-account');
                    Route::post('bank-account', [PlaidCompanyController::class, 'bulkUpdateBankAccount'])->name('api.plaid.company.bulk.update.bank-account');
                });
                Route::prefix('stack')->group(function () {
                    Route::get('not-linked-expenses', [PlaidCompanyController::class, 'showAllStackNotLinkedExpenses'])->name('api.plaid.company.stack.not-linked');
                    Route::get('suggested-expenses', [PlaidCompanyController::class, 'showAllStackSuggestedExpenses'])->name('api.plaid.company.stack.suggsted-expenses');
                });
                Route::prefix('search')->group(function () {
                    Route::get('transactions', [PlaidCompanyController::class, 'searchTransactions'])->name('api.plaid.company.search.transactions');
                    Route::get('subscriptions', [PlaidCompanyController::class, 'searchSubscriptions'])->name('api.plaid.company.search.subscriptions');
                });
                Route::get('subscriptions', [PlaidCompanyController::class, 'showAllSubscriptions'])->name('api.plaid.company.show-all-subscriptions');
                Route::get('subscriptions/upcoming-expenses', [PlaidCompanyController::class, 'showAllSubscriptionsUpcomingExpenses'])->name('api.plaid.company.show-all-subscriptions.upcoming-expenses');
                Route::put('subscription', [PlaidCompanyController::class, 'updateSubscription'])->name('api.plaid.company.subscription.update');
                Route::prefix('costs')->group(function () {
                    Route::get('subscriptions', [PlaidCompanyController::class, 'subscriptionsCosts'])->name('api.plaid.company.costs.subscriptions');
                    Route::get('category', [PlaidCompanyController::class, 'categoryCosts'])->name('api.plaid.company.costs.category');
                    Route::get('vendor', [PlaidCompanyController::class, 'vendorCosts'])->name('api.plaid.company.costs.vendor');
                });
                Route::prefix('{plaidBankAccount}')->where(['plaidBankAccount' => '[0-9]+'])->group(function () {
                    Route::delete('', [PlaidCompanyController::class, 'deleteBankAccount'])->name('api.plaid.company.bank-account.delete');
                    Route::post('sync', [PlaidCompanyController::class, 'syncBankAccount'])->name('api.plaid.company.bank-account.sync');
                    Route::post('', [PlaidCompanyController::class, 'updateBankAccount'])->name('api.plaid.company.bank-account.update');
                });
                Route::prefix('{plaidSubscription}')->where(['plaidSubscription' => '[0-9]+'])->group(function () {
                    Route::get('transactions', [PlaidCompanyController::class, 'showAllSubscriptionTransactions'])->name('api.plaid.company.subscription.transactions.show-all');
                });
                Route::prefix('filters')->group(function () {
                    Route::get('breakdown', [PlaidCompanyController::class, 'showBreakdownFilters'])->name('api.plaid.filters.breakdown');
                    Route::get('transactions', [PlaidCompanyController::class, 'showTransactionsFilters'])->name('api.plaid.filters.transactions');
                    Route::get('alert-notifications', [PlaidCompanyController::class, 'showAlertNotificationsFilters'])->name('api.plaid.filters.alert-notifications');
                    Route::get('subscriptions', [PlaidCompanyController::class, 'showSubscriptionsFilters'])->name('api.plaid.filters.subscriptions');
                    Route::get('subscriptions-upcoming-expenses', [PlaidCompanyController::class, 'showSubscriptionsUpcomingExpensesFilters'])->name('api.plaid.filters.subscriptions-upcoming-expenses');
                    Route::prefix('{plaidSubscription}')->where(['plaidSubscription' => '[0-9]+'])->group(function () {
                        Route::get('transactions', [PlaidCompanyController::class, 'showSubscriptionTransactionsFilters'])->name('api.plaid.filters.subscription.transactions');
                    });
                });
                Route::prefix('expenses')->group(function () {
                    Route::get('export-to-csv', [PlaidCompanyController::class, 'exportExpensesToCsv'])->name('api.plaid.company.expenses.export-to-csv');
                    Route::get('overtime', [PlaidCompanyController::class, 'expensesOverTime'])->name('api.plaid.company.expenses.overtime');
                    Route::get('breakdown', [PlaidCompanyController::class, 'expensesBreakdown'])->name('api.plaid.company.expenses.breakdown');
                    Route::get('upcoming-summary', [PlaidCompanyController::class, 'expensesUpcomingSummary'])->name('api.plaid.company.expenses.upcoming-summary');
                    Route::prefix('overview')->group(function () {
                        Route::get('summary', [PlaidCompanyController::class, 'expenseOverviewSummary'])->name('api.plaid.company.expenses.overview.summary');
                        Route::get('overtime', [PlaidCompanyController::class, 'expensesOverviewOverTime'])->name('api.plaid.company.expenses.overview.overtime');
                        Route::get('breakdown', [PlaidCompanyController::class, 'expensesOverviewBreakdown'])->name('api.plaid.company.expenses.overview.breakdown');
                    });
                    Route::get('contract-cost-overtime', [PlaidCompanyController::class, 'expensesContractCostOvertime'])->name('api.plaid.company.expenses.contract.cost.overtime');
                });
            });
        });
    });
    // no auth apis
    Route::post('/login', [AuthenticatedSessionController::class, 'login']);
    Route::post('/confirm-email/', [UserController::class, 'confirmEmail']);
    Route::post('/resend-confirm-email', [UserController::class, 'resendConfirmEmail']);
    Route::post('/forgot-password', [ForgotPasswordController::class, 'forgotPassword']);
    Route::post('/reset-password', [ResetPasswordController::class, 'resetPassword']);
    Route::post('/confirm-password', [ConfirmablePasswordController::class, 'store']);
    Route::post('/two-factor-challenge', [TwoFALoginController::class, 'store']);
    Route::post('/two-factor-confirm', [TwoFALoginController::class, 'confirm']);
    Route::post('/contactus', [ContactUsController::class, 'store']);
    Route::get('/enum/{enumName}', [EnumController::class, 'showByEnum']);
    Route::get('/email/blacklist/{domain}', [EmailBlackListController::class, 'showIsBlacklisted']);
    Route::get('/search/company/hs/{companyName}', [CompanyHubspotController::class, 'showCompanyHubspot']);

    // Register
    Route::prefix('register')->group(function () {
        Route::post('/step-one', [RegisterUserController::class, 'stepOne']);
        Route::post('/step-two', [RegisterUserController::class, 'stepTwo']);
        Route::post('/step-three', [RegisterUserController::class, 'stepThree']);
        Route::post('/msp', [RegisterMSPController::class, 'registerMSP']);
        Route::post('/direct', [RegisterMSPController::class, 'registerDirect']);
    });

    // Activity logs
    Route::prefix('activity-logs')->group(function () {
        Route::post('/', [ActivityLogsController::class, 'store']);
    });

    // Analytics
    Route::prefix('analytics')->group(function () {
        Route::post('', [AnalyticsController::class, 'store']);
        Route::post('bulk-store', [AnalyticsController::class, 'bulkStore']);
        Route::put('update-video-view-time', [AnalyticsController::class, 'updateVideoViewTime']);
    });

    // Comments
    Route::prefix('comments')->group(function () {
        Route::get('', [CommentController::class, 'showAll']);
    });

    // Shout Out
    Route::prefix('shout-out')->group(function () {
        Route::get('/', [ShoutOutController::class, 'showAll']);
    });

    // Blog
    Route::get('blog/{friendly_url}', [BlogController::class, 'showByFriendlyUrl']);

    // Pitch events
    Route::prefix('pitch-events')->group(function () {
        Route::get('/', [PitchEventsController::class, 'showAll']);
        Route::post('/detail', [PitchEventsController::class, 'detail']);
        Route::get('/not-started', [PitchEventsController::class, 'notStarted']);
        Route::get('/not-started/{company_id}', [PitchEventsController::class, 'notStartedForCompany'])
            ->where('company_id', '[0-9]+');
        Route::get('/get-next', [PitchEventsController::class, 'getNext']);
        Route::post('/download-ics-file', [PitchEventsController::class, 'getICSFile']);
        Route::post('/last-pitch-scheduled-vendors', [VendorScheduleController::class, 'listPreviousPitchEventScheduledVendors']);
        Route::get('/last-ended/videos', [PitchEventsController::class, 'listLastEndedPitchEventVideos']);

        Route::prefix('schedule-vendor')->group(function () {
            Route::post('/', [VendorScheduleController::class, 'listPitchEventScheduledVendors']);
            Route::get('/last-pitch', [VendorScheduleController::class, 'lastPitchEventScheduledVendors']);
        });
        Route::prefix('/{pitchEvent}')->where(['pitchEvent' => '[0-9]+'])->group(function () {
            Route::get('/videos', [PitchEventsController::class, 'listAllVideos']);
            Route::get('/videos/vendor/{vendorId}', [PitchEventsController::class, 'listVideosForVendor'])
                ->where('vendorId', '[0-9]+');
        });
    });

    // Profiles
    Route::prefix('profiles')->group(function () {
        Route::prefix('rule')->group(function () {
            Route::prefix('value')->group(function () {
                Route::get('', [ProfileRuleValueController::class, 'showAll']);
                Route::get('company/{company:friendly_url}', [ProfileRuleValueController::class, 'showCompanyRulesByFriendlyUrl']);
                Route::get('user/{user:friendly_url}', [ProfileRuleValueController::class, 'showUserRulesByFriendlyUrl']);
            });
        });
        Route::prefix('user/{profileUser}')->where(['profileUser' => '[0-9]+'])->group(function () {
            Route::prefix('document')->group(function () {
                Route::get('', [UserProfileController::class, 'listProfileDocuments']);
                Route::get('{document}', [UserProfileController::class, 'downloadDocument'])
                    ->where(['document' => '[0-9]+']);
            });
        });
        Route::prefix('/company/{company}')->where(['company' => '[0-9]+'])->group(function () {
            Route::prefix('/content-feed')->group(function () {
                Route::get('', [MediaAndDocumentController::class, 'getCompanyContentFeed']);
                Route::get('/counts', [MediaAndDocumentController::class, 'getCompanyContentFeedCounts']);
            });
        });
        Route::prefix('/vendor-basic/{companyId}')->where(['companyId' => '[0-9]+'])->group(function () {
            Route::prefix('/overview')->group(function () {
                Route::get('/', [VendorProfileOverviewController::class, 'showAll']);
            });
            Route::prefix('contact-info')->group(function () {
                Route::get('', [VendorProfileContactInfoController::class, 'showAll']);
            });
            Route::prefix('contacts')->group(function () {
                Route::get('', [VendorProfileContactsController::class, 'showAll']);
            });
            Route::prefix('/video')->group(function () {
                Route::get('/', [VendorProfileVideoController::class, 'showAll']);
                Route::get('/{video}', [VendorProfileVideoController::class, 'downloadVideo'])
                    ->where(['video' => '[0-9]+']);
            });
            Route::prefix('/document')->group(function () {
                Route::get('/', [VendorProfileDocumentController::class, 'showAll']);
                Route::get('/{document}', [VendorProfileDocumentController::class, 'downloadDocument'])
                    ->where(['document' => '[0-9]+']);
            });
        });
    });

    // Profile enrichment
    Route::prefix('/profile-enrichment')->group(function () {
        Route::prefix('/company/{friendly_url}')->group(function () {
            Route::prefix('answers')->group(function () {
                Route::get('/', [ProfileEnrichmentCompanyAnswersController::class, 'showAll']);
            });
        });
    });

    // Company HERE
    Route::prefix('company')->group(function () {
        Route::get('companytypes', [CompanyController::class, 'companyTypes']);
        Route::post('search', [CompanyController::class, 'search']);
        Route::get('top-trending', [CompanyController::class, 'topTrending']);
        Route::get('avatars', [CompanyController::class, 'showHomePageAvatars']);
        Route::get('handle/{profile_vendor_handle}', [CompanyController::class, 'showByProfileVendorHandle']);
        Route::prefix('validate-invitation')->group(function () {
            Route::post('', [CompanyInviteController::class, 'validateInvitation']);
            Route::post('affiliate', [CompanyInviteController::class, 'validateInvitationAffiliate']);
        });
        Route::prefix('navigation_favorites')->group(function () {
            Route::post('add-update', [CompanyNavigationFavoritesController::class, 'addUpdateNavigationFavorites']);
        });
        Route::prefix('{company:friendly_url}')->group(function () {
            Route::get('marketplace', [CompanyMarketplacePartnerController::class, 'showAll']);
            Route::get('distributors', [CompanyMarketplacePartnerController::class, 'distributors']);
            Route::prefix('content')->group(function () {
                Route::get('filters', [CompanySearchContentController::class, 'getFilters']);
                Route::get('blogs', [CompanySearchContentController::class, 'getBlogs']);
                Route::get('documents', [CompanySearchContentController::class, 'getDocuments']);
                Route::get('media-galleries', [CompanySearchContentController::class, 'getMediaGalleries']);
                Route::get('videos', [CompanySearchContentController::class, 'getVideos']);
            });
        });
        Route::get('followers/{company}', [FollowCompanyController::class, 'companyFollowers'])
            ->where('company', '[0-9]+');
        Route::get('my-stack/filters', [MyStackCompanyFilterController::class, 'filters']);
        Route::prefix('{company}')->where(['company' => '[0-9]+'])->group(function () {
            Route::get('', [CompanyController::class, 'showById']);
            Route::get('product-categories', [CompanyController::class, 'productCategories']);
            Route::get('products', [CompanyController::class, 'showAllProducts'])->name('company.show-all-products.get');
            Route::prefix('vendor-or-product')->group(function () {
                Route::post('store', [CompanyController::class, 'storeVendorOrProduct'])->name('company.vendor-or-product.store');
            });
            Route::get('social', [CompanySocialController::class, 'showAll']);
            Route::prefix('affiliate')->group(function () {
                Route::prefix('my-stack')->group(function () {
                    Route::get('', [MyStackCompanyController::class, 'showAllAffiliate']);
                    Route::get('categories-filled', [MyStackCompanyController::class, 'showAllAffiliateCategoriesFilled']);
                });
            });
            Route::prefix('my-stack')->group(function () {
                Route::get('', [MyStackCompanyController::class, 'showAll']);
                Route::get('categories-filled', [MyStackCompanyController::class, 'showAllCategoriesFilled']);
                Route::get('missing-adoption-stack', [MyStackCompanyController::class, 'missingAdoptionStack']);
            });
            Route::prefix('product')->group(function () {
                Route::get('', [ProductController::class, 'showAll']);
                Route::prefix('{product}')->where(['product' => '[0-9]+'])->group(function () {
                    Route::get('', [ProductController::class, 'findById']);
                });
            });
        });
        Route::get('sidebar/{company:friendly_url}', [CompanyController::class, 'showSideBarUsingFriendlyUrl']);
        Route::prefix('{search_by}/{value}')->group(function () {
            Route::get('', [CompanyController::class, 'showByFriendlyUrl']);
            Route::get('customer-is-accessible', [CompanyController::class, 'customerIsAccessible']);
            Route::get('blogs', [CompanyController::class, 'showBlogs']);
        });
    });

    // Products
    Route::prefix('products')->group(function () {
        Route::get('', [ProductController::class, 'showAllProducts']);
        Route::get('search', [ProductController::class, 'searchProducts']);
        Route::get('graph-listing', [ProductController::class, 'graphListing']);
        Route::get('top-trending', [ProductController::class, 'topTrending']);
        Route::prefix('{product:friendly_url}')->group(function () {
            Route::get('', [ProductController::class, 'showByFriendlyUrl']);
            Route::get('reviews', [ProductController::class, 'loadProductReviews']);
        });
    });

    // Categories
    Route::prefix('categories')->group(function () {
        Route::get('', [CategoryController::class, 'showAll']);
        Route::get('popular', [CategoryController::class, 'popular']);
        Route::get('show-parents', [CategoryController::class, 'showParents']);
        Route::get('show-featured', [CategoryController::class, 'showFeatured']);
        Route::get('with-subcategories', [CategoryController::class, 'withSubcategories']);
        Route::prefix('{category}')->where(['category' => '[0-9]+'])->group(function () {
            Route::get('vendors', [CategoryController::class, 'showVendors']);
            Route::get('products', [CategoryController::class, 'showProducts']);
        });
    });

    // Tags
    Route::prefix('tags')->group(function () {
        Route::get('/', [TagController::class, 'showAll']);
    });

    // Streaming
    Route::get('/stream/{video}', [VideoController::class, 'streamVideo']);

    // Images
    Route::get('/image', [ImageController::class, 'getImage'])->name('get-image');

    // Languages
    Route::prefix('languages')->group(function () {
        Route::get('/', [LanguageController::class, 'showAll']);
    });

    // Countries
    Route::prefix('countries')->group(function () {
        Route::get('/', [CountryController::class, 'showAll']);
    });

    // Currencies
    Route::prefix('currencies')->group(function () {
        Route::get('', [CurrencyController::class, 'showAll']);
        Route::get('{currency}', [CurrencyController::class, 'show']);
    });
    // Filters
    Route::prefix('filters')->group(function () {
        Route::get('all-profile-types', [FilterController::class, 'getAllProfileTypes']);
        Route::get('companies-categories', [FilterController::class, 'getCompaniesCategories']);
        Route::get('company-profile-types', [FilterController::class, 'getCompanyProfileTypes']);
        Route::get('company-types', [FilterController::class, 'getCompanyTypes']);
        Route::get('currencies', [FilterController::class, 'getCurrencies']);
        Route::get('recurrences', [FilterRecurrenceController::class, 'getAllRecurrences']);
        Route::get('industry-calendar-presenters', [FilterController::class, 'getIndustryEventsPresenters']);
        Route::get('products-categories', [FilterController::class, 'getProductsCategories']);
        Route::get('used-categories', [FilterController::class, 'getUsedCategories']);
        Route::get('user-profile-types', [FilterController::class, 'getUserProfileTypes']);
        Route::get('top-video-categories-and-tags', [FilterController::class, 'getTopVideoCategoriesAndTags']);
        Route::get('vendors-categories', [FilterController::class, 'getVendorsCategories']);
        Route::get('videos-categories', [FilterController::class, 'getVideosCategories']);
        Route::get('affiliate-brands', [FilterController::class, 'getAffiliateBrand']);
        Route::prefix('contracts')->group(function () {
            Route::get('agreements', [FilterContractController::class, 'getAllContractAgreements']);
            Route::get('types', [FilterContractController::class, 'getAllContractTypes']);
            Route::get('client-vendors', [FilterContractController::class, 'getClientVendors']);
            Route::get('client-products', [FilterContractController::class, 'getClientProducts']);
            Route::get('billing-types', [FilterContractController::class, 'getAllContractBillingTypes']);
            Route::get('notification-types', [FilterContractController::class, 'getAllContractNotificationTypes']);
            Route::get('billing-type-options', [FilterContractController::class, 'getAllContractBillingTypeOptions']);
        });
        Route::prefix('chart')->group(function () {
            Route::get('metrics', [FilterChartController::class, 'getAllChartMetrics']);
            Route::get('pitch-events', [FilterChartController::class, 'getAllChartPitchEvents']);
            Route::prefix('pitch-poll')->group(function () {
                Route::get('dates', [FilterChartController::class, 'getAllChartPitchPollDates']);
                Route::get('products-categories', [FilterChartController::class, 'getChartPitchPollProductsCategories']);
                Route::get('companies', [FilterChartController::class, 'getChartPitchPollCompanies']);
                Route::get('vendors', [FilterChartController::class, 'getChartPitchPollVendors']);
            });
            Route::prefix('quadrants')->group(function () {
                Route::get('products-categories', [FilterChartController::class, 'getChartQuadrantsProductsCategories']);
            });
        });
    });

    // Configuration
    Route::prefix('configuration')->group(function () {
        Route::get('app/{key}', [AppConfigurationController::class, 'getAppConfigByKey']);
    });

    // Lookup options
    Route::prefix('lookups')->group(function () {
        Route::get('{lookupOption}/options', [LookupOptionController::class, 'loadData'])
            ->where(['lookupOption' => '[a-zA-Z0-9-_]+']);
    });

    // Charts
    Route::prefix('chart')->group(function () {
        Route::post('upload-seo-image', [ChartProductReviewsController::class, 'uploadSeoImage']);
        Route::prefix('pitch-poll')->group(function () {
            Route::get('', [ChartPitchPollController::class, 'loadData']);
        });
        Route::prefix('product-reviews')->group(function () {
            Route::get('', [ChartProductReviewsController::class, 'loadData']);
        });
        Route::prefix('quadrant')->group(function () {
            Route::get('', [ChartQuadrantController::class, 'loadData']);
        });
    });

    // Job Titles
    Route::prefix('job-titles')->group(function () {
        Route::get('', [JobTitleController::class, 'showAll']);
    });

    // User
    Route::prefix('user')->group(function () {
        Route::get('/handle/{handle}', [UserProfileController::class, 'showByProfileHandle']);
        Route::prefix('friendly-url/{friendly_url}')->group(function () {
            Route::get('/', [UserProfileController::class, 'showByProfileFriendlyUrl']);
            Route::get('/blogs', [UserProfileController::class, 'showBlogs']);
        });
        Route::prefix('{user}')->where(['user' => '[0-9]+'])->group(function () {
            Route::get('followers', [FollowUserController::class, 'userFollowers']);
            Route::post('following', [FollowUserController::class, 'userFollowing']);
            Route::post('following-widget', [FollowUserController::class, 'followingWidget']);
            Route::prefix('my-stack')->group(function () {
                Route::get('/', [MyStackUserController::class, 'showAll']);
            });
            Route::prefix('profile')->group(function () {
                Route::get('/', [UserProfileController::class, 'showProfileInfo']);
                Route::prefix('/content-feed')->group(function () {
                    Route::get('', [MediaAndDocumentController::class, 'getUserContentFeed']);
                    Route::get('/counts', [MediaAndDocumentController::class, 'getUserContentFeedCounts']);
                });
                Route::prefix('video')->group(function () {
                    Route::get('/', [UserProfileController::class, 'listProfileVideos']);
                });
                Route::prefix('broadcast-messages')->group(function () {
                    Route::get('', [BroadcastMessagesController::class, 'showAll']);
                    Route::put('/update', [BroadcastMessagesController::class, 'updateAllMessageStatus']);
                    Route::put('/update/{broadcastMessage}', [BroadcastMessagesController::class, 'updateMessageStatus'])
                        ->where('broadcastMessage', '[0-9]+');
                });
            });
        });
    });

    Route::prefix('explorer')->group(function () {
        Route::get('', [ExplorerController::class, 'loadExplorerPage']);
        Route::get('popular-categories', [ExplorerController::class, 'popularCategories']);
        Route::prefix('{category}')->where(['category' => '[0-9]+'])->group(function () {
            Route::get('videos', [ExplorerController::class, 'showVideosByCategory']);
            Route::post('user-profiles', [ExplorerController::class, 'showUserProfilesByCategory']);
            Route::post('vendor-profiles', [ExplorerController::class, 'showVendorProfilesByCategory']);
        });
        Route::prefix('videos')->group(function () {
            Route::get('', [ExplorerVideosPageController::class, 'loadExplorerVideosPage']);
            Route::get('category/{friendly_url}', [ExplorerVideosPageController::class, 'loadExplorerCategoryVideosPage']);
            Route::get('tag/{id}', [ExplorerVideosPageController::class, 'loadExplorerTagVideosPage'])
                ->where('id', '[0-9]+');
        });
        Route::prefix('profiles')->group(function () {
            Route::get('', [ExplorerProfilesController::class, 'loadPage']);
            Route::get('by-type/{type}', [ExplorerProfilesController::class, 'loadByType']);
        });
    });

    Route::prefix('search')->group(function () {
        Route::get('', [SearchController::class, 'search']);
        Route::post('simple', [SimpleSearchController::class, 'simpleSearch']);
        Route::get('get-subdomain-type', [SimpleSearchController::class, 'getSubdomainType']);
        Route::get('profile/{media}', [ProfileController::class, 'getProfileByMediaId'])
            ->where('media', '[0-9]+');
    });

    Route::prefix('shortener')->group(function () {
        Route::post('create', [ShortenerController::class, 'create']);
        Route::post('get-for-model', [ShortenerController::class, 'getForModel']);
    });

    // Media Galleries
    Route::prefix('media-gallery')->group(function () {
        Route::get('by-owner/{ownerId}', [MediaGalleryController::class, 'showByOwner'])
            ->where('ownerId', '[0-9]+');
    });

    // Reviews
    Route::prefix('reviews')->group(function () {
        Route::get('/most-recent', [ReviewController::class, 'getMostRecent']);
    });

    // Recommended Addresses
    Route::prefix('google')->group(function () {
        Route::get('get-recommended-addresses', [GoogleApiController::class, 'getRecommendedAddresses']);
        Route::get('get-place-details', [GoogleApiController::class, 'getPlaceDetails']);
    });

    // Industry events
    Route::prefix('industry-events')->group(function () {
        Route::get('', [IndustryEventsController::class, 'showForCalendar']);
        Route::get('/export-calendar-to-csv', [IndustryEventsController::class, 'exportIndustryEvents']);
        Route::post('/unsubscribe-with-token', [IndustryEventsController::class, 'unsubscribeEmailWithToken']);
        Route::get('filters', [IndustryEventsController::class, 'filters']);
    });

    // Advertisement
    Route::prefix('advertisement')->group(function () {
        Route::get('', [AdvertisementController::class, 'loadForPage']);
        Route::prefix('available-location')->group(function () {
            Route::get('', [AdvertisementAvailableLocationController::class, 'showAll']);
        });
    });

    // Tags
    Route::prefix('time-zone')->group(function () {
        Route::get('/', [TimeZoneController::class, 'showAll']);
    });

    // Channel Deals
    Route::prefix('channel-deals')->group(function () {
        Route::get('/filters', [ChannelDealsController::class, 'showAllFilters'])->name('channel-deals.filters');
        Route::get('/show-current-deals', [ChannelDealsController::class, 'showCurrentDeals'])->name('channel-deals.show-current-deals');

        Route::prefix('claimed')->group(function () {
            Route::post('/store', [ChannelDealsClaimedController::class, 'store'])->name('channel-deals.claimed.store');
        });
    });

    // Whitelabeling
    Route::prefix('whitelabeling/{company:subdomain}')->group(function () {
        Route::get('', [WhitelabelingController::class, 'loadBySubdomain']);
    });

    Route::post('plaid/webhook', [PlaidWebhookController::class, 'handle'])->name('api.plaid.webhook');
});
