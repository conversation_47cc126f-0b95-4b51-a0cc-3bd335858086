<?php

namespace App\Services\Permission;

use App\Enums\Cache\CacheTTLEnum;
use App\Models\Permission\Role\Role;
use App\Models\Permission\Role\RoleUser;
use App\Services\Redis\RedisService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class RoleService
{
    /**
     * Get a role by ID with caching
     */
    public static function getRoleById(string $roleId): ?Role
    {
        $cacheKey = "role_{$roleId}";

        return Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK), function () use ($roleId) {
            return Role::with(['permissionGroups', 'templateRole'])->find($roleId);
        });
    }

    /**
     * Get roles by company ID with caching
     */
    public static function getRolesByCompanyId(string $companyId): Collection
    {
        $cacheKey = "company_roles_{$companyId}";

        return Cache::remember($cacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK), function () use ($companyId) {
            return Role::where('company_id', $companyId)
                ->with(['permissionGroups', 'templateRole'])
                ->get();
        });
    }

    /**
     * Get roles by user ID with caching
     */
    public static function getRolesByUserId(string $userId, ?string $asCompanyId = null): Collection
    {
        $userRolesCacheKey = $asCompanyId ? "user_roles_{$userId}_{$asCompanyId}" : "user_roles_{$userId}";

        return Cache::remember($userRolesCacheKey, now()->addSeconds(CacheTTLEnum::CACHE_TTL_WEEK),
            function () use ($userId, $asCompanyId) {
                return Role::whereHas('users', function ($query) use ($userId) {
                    $query->where('users.id', $userId);
                })
                    ->with(['permissionGroups', 'templateRole'])
                    ->when($asCompanyId, function ($query) use ($asCompanyId) {
                        $query->where('roles.company_id', $asCompanyId);
                    })
                    ->get();
            });
    }

    /**
     * Update only the affected company's roles in the global cache
     */
    public static function refreshRoleInGlobalCache(Role $role): void
    {
        $usersIds = RoleUser::where('role_id', $role->id)->get()->pluck('user_id')->toArray();
        foreach ($usersIds as $userId) {
            $permissionsGroupsCacheKey = "pgfuc_{$userId}_{$role->company_id}";
            Cache::forget($permissionsGroupsCacheKey);
            $adminPermissionsGroupsCacheKey = "apgfuc_{$userId}_{$role->company_id}";
            Cache::forget($adminPermissionsGroupsCacheKey);
            $inheritedPermissionsCacheKey = "inherited_user_permissions_{$userId}_{$role->company_id}";
            Cache::forget($inheritedPermissionsCacheKey);
        }

        $permissionIds = RoleUser::where('role_id', $role->id)->get()->pluck('user_id')->toArray();
        foreach ($permissionIds as $permissionId) {
            $userPermissionsCountCacheKey = "user_permissions_count_{$userId}_{$permissionId}";
            Cache::forget($userPermissionsCountCacheKey);
        }
        RedisService::flushAllRedisPermissionsAndRoles();

        // Log cache invalidation for debugging
        Log::debug("Updated cache for role ID: {$role->id}, company ID: {$role->company_id}");
    }

    /**
     * Update only the affected company's roles in the global cache
     */
    public static function refreshRoleUserInGlobalCache(string $roleId, string $userId, string $companyId): void
    {
        $permissionsGroupsCacheKey = "pgfuc_{$userId}_{$companyId}";
        Cache::forget($permissionsGroupsCacheKey);
        $adminPermissionsGroupsCacheKey = "apgfuc_{$userId}_{$companyId}";
        Cache::forget($adminPermissionsGroupsCacheKey);
        $inheritedPermissionsCacheKey = "inherited_user_permissions_{$userId}_{$companyId}";
        Cache::forget($inheritedPermissionsCacheKey);
        $userRolesCacheKey = "user_roles_{$userId}";
        Cache::forget($userRolesCacheKey);

        $permissionIds = RoleUser::where('role_id', $roleId)->get()->pluck('user_id')->toArray();
        foreach ($permissionIds as $permissionId) {
            $userPermissionsCountCacheKey = "user_permissions_count_{$userId}_{$permissionId}";
            Cache::forget($userPermissionsCountCacheKey);
        }

        // Log cache invalidation for debugging
        Log::debug("Updated cache for role ID: {$roleId}, company ID: {$companyId}, user ID: {$userId}");
    }
}
