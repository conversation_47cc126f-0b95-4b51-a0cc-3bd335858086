<?php

namespace App\Http\Controllers\Api\Admin\Migration;

use App\Enums\Contract\ContractTypes;
use App\Http\Controllers\Controller;
use App\Http\Requests\Migration\Contract\MigrateContractsToAggregatorStructureRequest;
use App\Jobs\Migrate\MigrateProductContractsNames;
use App\Jobs\Migrate\MigrateVendorContractsNames;
use App\Models\Contract\Contract;
use App\Models\Contract\ContractType;
use App\Services\AuthService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateContractsController extends Controller
{
    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/migrations/contracts/migrate",
     *     operationId="contracts/migrate",
     *     tags={"MigrateContractsController"},
     *     summary="Migrates all contracts from the old structure to aggregators structure",
     *     description="Migrates all contracts from the old structure to aggregators structure",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}/api/v1/admin/migrations/contracts/migrate
    // Bearer token needed
    public function migrateContracts(MigrateContractsToAggregatorStructureRequest $request)
    {
        if (!AuthService::userIsSuperAdmin()) {
            abort(403, config('genericMessages.error.UNAUTHORIZED'));
        }
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Starting contracts migration to aggregator structure');
        // Getting the aggregator type
        $contractType = ContractType::firstWhere(['key' => ContractTypes::CONTRACT]);
        if (!$contractType) {
            throw new Exception('The contract type ' . ContractTypes::CONTRACT . ' was not found.');
        }
        // Getting the product contract type
        $contractProductType = ContractType::firstWhere(['key' => ContractTypes::PRODUCT_CONTRACT]);
        if (!$contractProductType) {
            throw new Exception('The contract type ' . ContractTypes::PRODUCT_CONTRACT . ' was not found.');
        }
        // Getting contracts
        $contracts = Contract::where('contract_type_id', $contractType->id)
            ->whereDoesntHave('productContracts', function ($q) use ($contractProductType) {
                $q->where('contract_type_id', $contractProductType->id);
            })
            ->whereNull('custom_properties->migrated_at')
            ->select('id', 'name', 'contract_type_id', 'author_id', 'owner_id', 'company_id',
                'product_id', 'client_product_id', 'client_vendor_id', 'custom_properties', 'currency_id'
            )
            ->with([
                'addOns:id,parent_id,contract_type_id,author_id,owner_id,company_id,client_vendor_id,product_id,client_product_id,custom_properties,currency_id',
                'company:id,name',
                'clientVendor:id,name,is_distributor',
            ]);
        $totalContractsCount = $contracts->count();
        if ($totalContractsCount === 0) {
            return response()->json(['message' => 'There are no contracts to migrate.']);
        }
        // Appliing limit
        $contracts->when($request->has('limit'), function ($q) use ($request) {
            $q->limit($request->limit);
        });

        $contracts = $contracts->get();
        $contractsCount = $contracts->count();
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Migrating ' . $contractsCount . ' contract' . ($contractsCount !== 1 ? 's' : ''));

        $success = 0;
        $failed = 0;
        foreach ($contracts as $contract) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Migrating Contract (ID = ' . $contract->id . ')');
            $result = $this->migrateContractToAggregatorStructure(
                $contract,
                $contractType->id,
                $contractProductType->id
            );
            if ($result) {
                $success++;
            } else {
                $failed++;
            }
        }
        $message = $contractsCount . ' Selected Contract' . ($contractsCount !== 1 ? 's' : '') . '. ';
        $message .= $success . ' Migrated. ' . $failed . ' Failed. ' . ($totalContractsCount - $success) . ' Remaining.';
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Finishing migration.' . $message);
        $consistencyReport = $this->generateConsistencyReport();

        return response()->json([
            'message' => $message,
            'consistency' => $consistencyReport,
        ]);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/migrations/contracts/check-consistency",
     *     operationId="contracts/checkConsistency",
     *     tags={"MigrateContractsController"},
     *     summary="Generate a report of contracts data consistency",
     *     description="Generate a report of contracts data consistency",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}/api/v1/admin/migrations/contracts/check-consistency
    // Bearer token needed
    public function checkConsistency()
    {
        if (!AuthService::userIsSuperAdmin()) {
            abort(403, config('genericMessages.error.UNAUTHORIZED'));
        }
        $consistencyReport = $this->generateConsistencyReport();

        return response($consistencyReport);
    }

    /**
     * Rules:
     *  - There must be no contracts of type ADDON;
     *  - All contracts of type PRODUCT_CONTRACT must have a related product;
     *  - All contracts of type PRODUCT_CONTRACT must have a parent contract of type CONTRACT;
     *  - All contracts of type CONTRACT must have a related vendor;
     */
    private function generateConsistencyReport()
    {
        $firstMigrated = Contract::whereNotNull('custom_properties->migrated_at')
            ->orderBy('custom_properties->migrated_at', 'asc')
            ->first();
        if (!$firstMigrated || !isset($firstMigrated->custom_properties['migrated_at'])) {
            return 'No migrated contracts were found.';
        }
        $firstMigrationDate = Carbon::createFromFormat('Y-m-d H:i', $firstMigrated->custom_properties['migrated_at']);

        // Getting the aggregator type
        $contractType = ContractType::firstWhere(['key' => ContractTypes::CONTRACT]);
        if (!$contractType) {
            throw new Exception('The contract type ' . ContractTypes::CONTRACT . ' was not found.');
        }
        // Getting the product contract type
        $productContractType = ContractType::firstWhere(['key' => ContractTypes::PRODUCT_CONTRACT]);
        if (!$productContractType) {
            throw new Exception('The contract type ' . ContractTypes::PRODUCT_CONTRACT . ' was not found.');
        }
        // Getting the addOn contract type
        $addOnType = ContractType::firstWhere(['key' => ContractTypes::ADDON]);
        if (!$addOnType) {
            throw new Exception('The contract type ' . ContractTypes::ADDON . ' was not found.');
        }

        $response = '';
        // Checking for add ons
        $addOnCount = Contract::where('contract_type_id', $addOnType->id)->count();
        if ($addOnCount > 0) {
            $response .= 'ADD ONS - FAIL: ' . $addOnCount . " AddOns found.\n";
        } else {
            $response .= "ADD ONS - SUCCESS: No AddOns found.\n";
        }
        // Checking if all product contracts has products
        $productContractsWithoutProduct = Contract::where('contract_type_id', $productContractType->id)
            ->whereNull('product_id')
            ->whereNull('client_product_id')
            ->count();
        if ($productContractsWithoutProduct > 0) {
            $response .= 'PRODUCT CONTRACTS #1 - FAIL: ' . $productContractsWithoutProduct . " do not have a related product.\n";
        } else {
            $response .= "PRODUCT CONTRACTS #1 - SUCCESS: All Product Contracts have a related product.\n";
        }
        // Checking if all product contracts has a related vendor contract
        $productContractsWithoutParent = Contract::where('contract_type_id', $productContractType->id)
            ->whereDoesntHave('parent')->count();
        if ($productContractsWithoutParent > 0) {
            $response .= 'PRODUCT CONTRACTS #2 - FAIL: ' . $productContractsWithoutParent . " are not related to a Vendor Contract.\n";
        } else {
            $response .= "PRODUCT CONTRACTS #2 - SUCCESS: All Product Contracts are related to a Vendor Contract.\n";
        }
        // Checking if all vendor contracts has vendors
        $contractsWithoutVendor = Contract::where('contract_type_id', $contractType->id)
            ->whereNull('company_id')
            ->whereNull('client_vendor_id')
            ->count();
        if ($contractsWithoutVendor > 0) {
            $response .= 'VENDOR CONTRACTS - FAIL: ' . $contractsWithoutVendor . " do not have a related product.\n";
        } else {
            $response .= "VENDOR CONTRACTS - SUCCESS: All Vendor Contracts have a related vendor.\n";
        }

        return $response;
    }

    /**
     * Migrates a given contract to a new aggregator structure by creating a duplicate aggregator contract,
     * updating the original contract with a new type and parent ID, and updating any associated AddOns.
     * Logs the migration process and handles database transactions for consistency.
     *
     * @param  Contract  $contract  The contract to be migrated.
     * @param  string  $contractTypeId  The ID of the new contract type for the aggregator.
     * @param  string  $productContractTypeId  The ID of the new contract type for the product contract.
     * @return bool Returns true if the migration was successful, otherwise false.
     */
    private function migrateContractToAggregatorStructure(
        Contract $contract,
        string $contractTypeId,
        string $productContractTypeId,
    ): bool {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Migrating Contract (ID = ' . $contract->id . ')');
        DB::beginTransaction();

        try {
            $migratedAt = Carbon::now()->format('Y-m-d H:i');
            // Creating the duplicate
            $isClientContract = !is_null($contract->client_vendor_id) || !is_null($contract->client_product_id);
            $contractName = $contract->company?->name ?? $contract->clientVendor?->name;
            $aggregator = Contract::create([
                'contract_type_id' => $contractTypeId,
                'name' => $contractName,
                'author_id' => $contract->author_id,
                'owner_id' => $contract->owner_id,
                'company_id' => $contract->company_id,
                'client_vendor_id' => $contract->client_vendor_id,
                'currency_id' => $contract->currency_id,
                'custom_properties' => [
                    'migrated_at' => $migratedAt,
                    'is_client_contract' => $isClientContract,
                ],
            ]);
            Log::debug($source . 'Aggregator contract created ID: ' . $aggregator->id . ' for vendor ID: ' . $aggregator->company_id);
            $contractProductProperties = $contract->custom_properties ?? [];
            $contractProductProperties['previous_type_id'] = $contract->contract_type_id;
            $contractProductProperties['previous_parent_id'] = $contract->parent_id;
            $contractProductProperties['migrated_at'] = $migratedAt;
            Log::debug($source . 'Updating contract (ID = ' . $contract->id . ')');
            $newContractData = [
                'contract_type_id' => $productContractTypeId,
                'parent_id' => $aggregator->id,
                'custom_properties' => $contractProductProperties,
                'is_client_contract' => $isClientContract,
            ];
            Log::debug($source . ' - New contract data: ' . json_encode($newContractData));
            // Updating the contract
            $contract->update($newContractData);
            // Updating all existing AddOns for current contract
            if (!empty($contract->addOns)) {
                foreach ($contract->addOns as $addOn) {
                    Log::debug($source . 'Updating AddOn (ID = ' . $addOn->id . ')');
                    $addOnProperties = $addOn->custom_properties ?? [];
                    $addOnProperties['previous_type_id'] = $addOn->contract_type_id;
                    $addOnProperties['previous_parent_id'] = $addOn->parent_id;
                    $addOnProperties['migrated_at'] = $migratedAt;
                    $newAddData = [
                        'contract_type_id' => $productContractTypeId,
                        'parent_id' => $aggregator->id,
                        'custom_properties' => $addOnProperties,
                    ];
                    Log::debug($source . ' - New AddOn data: ' . json_encode($newAddData));
                    $addOn->update($newAddData);
                }
            }
            DB::commit();
            Log::debug($source . 'Finishing Migration for contract (ID = ' . $contract->id . ')');

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($source . 'Error migrating contract: ' . $e->getMessage());

            return false;
        }
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/migrations/contracts/rollback",
     *     operationId="contracts/rollback",
     *     tags={"MigrateContractsController"},
     *     summary="Rollback all migrated contracts from the aggregators structure to the old structure",
     *     description="Rollback all migrated contracts from the aggregators structure to the old structure",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}/api/v1/admin/migrations/contracts/rollback
    // Bearer token needed
    public function rollbackContracts()
    {
        if (!AuthService::userIsSuperAdmin()) {
            abort(403, config('genericMessages.error.UNAUTHORIZED'));
        }
        // Getting contracts
        $contracts = Contract::whereHas('contractType', function ($q) {
            $q->where('key', ContractTypes::CONTRACT);
        })
            ->whereNotNull('custom_properties->migrated_at')
            ->select('id', 'name', 'contract_type_id', 'company_id', 'product_id', 'custom_properties')
            ->with([
                'productContracts:id,parent_id,contract_type_id,company_id,product_id,custom_properties',
            ]);

        $contracts = $contracts->get();
        $contractsCount = $contracts->count();

        $success = 0;
        $failed = 0;
        foreach ($contracts as $contract) {
            Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Dispatching Job for Contract ID: ' . $contract->id);
            $result = $this->rollbackContractFromAggregatorStructure($contract);
            if ($result) {
                $success++;
            } else {
                $failed++;
            }
        }
        $message = $contractsCount . ' Selected Contract' . ($contractsCount !== 1 ? 's' : '') . '. ';
        $message .= $success . ' Rolled Back. ' . $failed . ' Failed.';
        Log::debug(__CLASS__ . '::' . __FUNCTION__ . '::Finishing rollback.' . $message);

        return response()->json(['message' => $message]);
    }

    private function rollbackContractFromAggregatorStructure(Contract $contract): bool
    {
        $source = __CLASS__ . '::' . __FUNCTION__ . '::';
        Log::debug($source . 'Contract ID: ' . $contract->id);
        DB::beginTransaction();

        try {
            // Updating all contract products for current aggregator contract
            if (!empty($contract->productContracts)) {
                foreach ($contract->productContracts as $productContract) {
                    Log::debug($source . 'Rolling back product contract (ID = ' . $productContract->id . ')');
                    $customProperties = $productContract->custom_properties ?? [];
                    $customProperties['rolled_back_at'] = Carbon::now();

                    $contractTypeId = $customProperties['previous_type_id'];
                    $contractParentId = $customProperties['previous_parent_id'];
                    unset($customProperties['previous_type_id'], $customProperties['previous_parent_id'], $customProperties['migrated_at']);

                    $productContractData = [
                        'contract_type_id' => $contractTypeId,
                        'parent_id' => $contractParentId,
                        'custom_properties' => $customProperties,
                    ];
                    Log::debug($source . ' - New product contract data: ' . json_encode($customProperties));

                    $productContract->update($productContractData);
                }
            }
            // Deleting aggregator contract
            $contractId = $contract->id;
            Log::debug($source . 'Deleting Aggregator Contract (ID = ' . $contractId . ')');
            $contract->delete();
            DB::commit();
            Log::debug($source . 'Finishing Rollback for contract (ID = ' . $contractId . ')');

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($source . 'Error rolling back contract: ' . $e->getMessage());

            return false;
        }
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/admin/migrations/contracts/rename-migrated-contracts",
     *     operationId="contracts/rename-migrated-contracts",
     *     tags={"MigrateContractsController"},
     *     summary="Rename all Migrated Contracts to add a name and/or an identifier number",
     *     description="Rename all Migrated Contracts to add a name and/or an identifier number",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}/api/v1/admin/migrations/contracts/rename-migrated-contracts
    // Bearer token needed
    public function renameMigratedContracts()
    {
        if (!AuthService::userIsSuperAdmin()) {
            abort(403, config('genericMessages.error.UNAUTHORIZED'));
        }
        MigrateVendorContractsNames::dispatch();
        MigrateProductContractsNames::dispatch();

        return response()->json([
            'message' => 'Jobs dispatched: MigrateVendorContractsName, MigrateProductContractsNames',
        ]);
    }
}
