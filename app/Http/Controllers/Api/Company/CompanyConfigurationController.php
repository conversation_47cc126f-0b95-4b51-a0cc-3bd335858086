<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\Configurations\ConfigurationsUpdateRequest;
use App\Http\Resources\Company\CompanyConfigurationResource;
use App\Models\Company\Company;
use App\Services\Contract\ContractService;

class CompanyConfigurationController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/config",
     *     operationId="company/config/showAll",
     *     tags={"CompanyConfigController"},
     *     summary="Show all Company Configurations",
     *     description="Show all Company Configurations",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // {{server}}api/v1/company/{company}/config
    // Bearer token needed
    public function showAll(Company $company): CompanyConfigurationResource
    {
        $config = $company->getConfig();

        return new CompanyConfigurationResource($config);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/company/{company}/config/update",
     *     operationId="company/config/update",
     *     tags={"CompanyConfigurationController"},
     *     summary="Update the configurations for a Company",
     *     description="Update the configurations for a Company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // {{server}}api/v1/company/{company}/config/update
    // Bearer token needed
    public function update(ConfigurationsUpdateRequest $request, Company $company): CompanyConfigurationResource
    {
        $config = $company->getConfig();
        $config->update($request->validated());
        $config->refresh();

        if ($request->filled('currency_id')) {
            ContractService::forgetContractsCache($company->id);
        }
        return new CompanyConfigurationResource($config);
    }
}
