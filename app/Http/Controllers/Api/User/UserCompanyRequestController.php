<?php

namespace App\Http\Controllers\Api\User;

use App\Enums\User\UserCompanyRequestLookupOptionsEnum;
use App\Enums\User\UserCompanyRequestStatusEnum;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\User\AdminUserChangeFlagStatusRequest;
use App\Http\Requests\User\UserCompanyRequestAdminSearchRequest;
use App\Http\Requests\User\UserCompanyRequestApproveRequest;
use App\Http\Requests\User\UserCompanyRequestCompanySearchRequest;
use App\Http\Requests\User\UserCompanyRequestDeclineRequest;
use App\Http\Resources\Admin\Lookup\AdminLookupOptionValueResource;
use App\Http\Resources\User\UserCompanyRequestAdminResource;
use App\Http\Resources\User\UserCompanyRequestCompanyResource;
use App\Models\Company\Company;
use App\Models\Lookup\LookupOptionValue;
use App\Models\UserCompanyRequest;
use App\Services\AuthService;
use App\Services\User\UserCompanyRequestService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

class UserCompanyRequestController extends Controller
{
    private array $relations = [
        'user', 'user.avatar', 'user.redFlaggedBy', 'user.redFlaggedBy.avatar',
        'company', 'company.avatar', 'reviewer', 'reviewer.avatar',
    ];

    /**
     * Methods for admins
     */

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/admin/user/requests/filters",
     *     operationId="admin/user/requests/showAllFilters",
     *     tags={"UserCompanyRequestController"},
     *     summary="Get available filters for user requests",
     *     description="Get available filters for user requests",
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: GET
    // {{server}}api/v1/admin/user/requests/filters
    // NO PARAMS
    //needs Bearer Token
    public function adminShowAllFilters(): JsonResponse
    {
        $result = UserCompanyRequestService::loadFiltersForAdmins();

        return response()->json($result);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/admin/user/requests/search",
     *     operationId="admin/user/requests/adminSearchRequests",
     *     tags={"UserCompanyRequestController"},
     *     summary="Get requests for based on filter values",
     *     description="Get requests for based on filter values",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/admin/user/requests/search
    // Bearer token needed
    public function adminSearchRequests(UserCompanyRequestAdminSearchRequest $request): AnonymousResourceCollection
    {
        $query = UserCompanyRequestService::loadUserCompanyRequests(
            $this->relations,
            $request->search_word ?? '',
            $request->statuses ?? null,
            $request->companies ?? null,
        )->selectRaw('user_company_requests.*');
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
            $result->setCollection($pageResults);
        }

        return UserCompanyRequestAdminResource::collection($result);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/admin/user/requests/flag",
     *     operationId="admin/user/requests/adminFlagRequest",
     *     tags={"UserCompanyRequestController"},
     *     summary="Change flag status of a user request",
     *     description="Change flag status of a user request",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: PUT
    // {{server}}admin/user/requests/flag
    // Bearer token needed
    public function adminFlagRequest(AdminUserChangeFlagStatusRequest $request): UserCompanyRequestAdminResource
    {
        $loggedUser = AuthService::getAuthUser();
        $redFlagged = (bool)$request->red_flagged;
        $redFlagReason = $request->red_flag_reason ?? "";

        $userRequest = UserCompanyRequest::find($request->request_id);
        if (!$userRequest) {
            throw ValidationException::withMessages(['Request not found.']);
        }
        if ($userRequest->status === UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::approved)) {
            throw ValidationException::withMessages(["This request cannot be flagged"]);
        }

        UserCompanyRequestService::flagRequest($userRequest, $loggedUser, $redFlagged, $redFlagReason);
        $userRequest->refresh()->load($this->relations);

        return new UserCompanyRequestAdminResource($userRequest);
    }

    /**
     * Methods for companies
     */

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/users/requests/search",
     *     operationId="company/users/requests/searchRequests",
     *     tags={"UserCompanyRequestController"},
     *     summary="Get requests for a company",
     *     description="Get requests for a copany",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/company/{company}/users/requests/search
    // Bearer token needed
    public function searchRequests(
        UserCompanyRequestCompanySearchRequest $request,
        Company $company
    ): AnonymousResourceCollection {
        $query = UserCompanyRequestService::loadUserCompanyRequests(
            $this->relations,
            $request->search_word ?? '',
            [
                UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::unverified),
                UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::declined),
                UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::flagged),
            ]
        )
            ->where('user_company_requests.company_id', $company->id)
            ->selectRaw('user_company_requests.*');
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
            $result->setCollection($pageResults);
        }

        return UserCompanyRequestCompanyResource::collection($result);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/users/requests/approve",
     *     operationId="company/users/requests/approve",
     *     tags={"DealController"},
     *     summary="Approve a user's request for a company",
     *     description="Approve a user's request for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/company/{company}/users/requests/approve
    // Bearer token needed
    public function approve(UserCompanyRequestApproveRequest $request, Company $company): UserCompanyRequestCompanyResource
    {
        $loggedUser = AuthService::getAuthUser();
        $userRequest = UserCompanyRequest::find($request->request_id);
        if (!$userRequest) {
            throw ValidationException::withMessages(["Request not found"]);
        }
        UserCompanyRequestService::validateRequestIsFromCompany($company, $userRequest);
        if ($userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::unverified) &&
            $userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::declined) &&
            $userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::flagged)) {
            throw ValidationException::withMessages(["This request cannot be approved"]);
        }
        // Updating request status
        UserCompanyRequestService::approveRequest($loggedUser, $userRequest, $request->role_id, $company);
        $userRequest->refresh()->load($this->relations);

        return new UserCompanyRequestCompanyResource($userRequest);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{company}/users/requests/decline",
     *     operationId="company/users/requests/decline",
     *     tags={"DealController"},
     *     summary="Decline a user's request for a company",
     *     description="Decline a user's request for a company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     * @throws Exception
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/company/{company}/users/requests/decline
    // Bearer token needed
    public function decline(UserCompanyRequestDeclineRequest $request, Company $company): UserCompanyRequestCompanyResource
    {
        $loggedUser = AuthService::getAuthUser();
        $userRequest = UserCompanyRequest::find($request->request_id);
        if (!$userRequest) {
            throw ValidationException::withMessages(["Request not found"]);
        }
        UserCompanyRequestService::validateRequestIsFromCompany($company, $userRequest);
        if ($userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::unverified) &&
            $userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::approved) &&
            $userRequest->status !== UserCompanyRequestStatusEnum::getKey(UserCompanyRequestStatusEnum::flagged)) {
            throw ValidationException::withMessages(["This request cannot be declined"]);
        }
        // Updating request status
        UserCompanyRequestService::declineRequest(
            $loggedUser,
            $userRequest,
            $request->reason,
            $request->reason_description ?? ''
        );
        $userRequest->refresh()->load($this->relations);

        return new UserCompanyRequestCompanyResource($userRequest);
    }

    //<editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/users/requests/options",
     *     operationId="company/users/showRequestsOptions",
     *     tags={"UserCompanyRequestController"},
     *     summary="Get available reasons for reject a request",
     *     description="Get available reasons for reject a request",
     *
     *     @OA\RequestBody(
     *         required=true
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    //</editor-fold>
    // API Call: POST
    // {{server}}api/v1/company/{company}/users/requests/options
    //needs Bearer Token
    public function showUserRequestRejectionOptions(): AnonymousResourceCollection
    {
        $values = LookupOptionValue::whereHas('lookupOption', function ($q) {
            $q->where('name', UserCompanyRequestLookupOptionsEnum::reject);
        })->get();

        return AdminLookupOptionValueResource::collection($values);
    }
}
