<?php

namespace App\Http\Controllers\Api\MyStack;

use App\Enums\CSV\CSVColumnsEnum;
use App\Helpers\PermissionsHelper;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\CustomerStack\CustomerStackDeleteRequest;
use App\Http\Requests\CustomerStack\CustomerStackShowAllRequest;
use App\Http\Requests\CustomerStack\CustomerStackStoreRequest;
use App\Http\Requests\CustomerStack\CustomerStackUpdatePartnerStatusRequest;
use App\Http\Requests\CustomerStack\CustomerStackUpdateRequest;
use App\Http\Resources\CustomerStack\CustomerStackResource;
use App\Models\Company\Company;
use App\Models\MyStack\CustomerStack;
use App\Services\AuthService;
use App\Services\ClientProduct\ClientProductService;
use App\Services\ClientVendor\ClientVendorService;
use App\Services\Company\CompanyService;
use App\Services\MyStack\MyStackCustomerService;
use App\Services\MyStack\MyStackService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class MyStackCustomerController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/customer/{company}/my-stack",
     *     operationId="customer/{company}/my-stack/showAll",
     *     tags={"MyStackCustomerController"},
     *     summary="Show all stack for a particular company",
     *     description="Show all stack for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAll(CustomerStackShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsClientMSP($company);

        $result = MyStackCustomerService::loadStackForCompany($company, $request);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        MyStackService::appendStackedCategories($pageResults);
        MyStackCustomerService::appendStackedProducts($pageResults);

        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return CustomerStackResource::collection($result);
    }

// <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/customer/{company}/my-stack/categories-filled",
     *     operationId="customer/{company}/my-stack/showAllCategoriesFilled",
     *     tags={"MyStackCustomerController"},
     *     summary="Show all categories filled for a particular company",
     *     description="Show all categories filled for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/categories-filled
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAllCategoriesFilled(CustomerStackShowAllRequest $request, Company $company): JsonResponse
    {
        CompanyService::validateCompanyIsClientMSP($company);

        $result = MyStackCustomerService::loadStackForCompany($company, $request);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $numOfCategoriesFilled = $pageResults->pluck('pivot.category_id')->filter()->unique()->count();

        return response()->json([
            'num_of_categories_filled' => $numOfCategoriesFilled,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/customer/{company}/my-stack/store",
     *     operationId="customer/{company}/my-stack/store",
     *     tags={"MyStackCustomerController"},
     *     summary="Store an array of companies to the company stack",
     *     description="Store an array of companies to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/store
    // Bearer token needed
    public function store(CustomerStackStoreRequest $request, Company $company): AnonymousResourceCollection
    {
        $loggedUser = AuthService::getAuthUser();
        CompanyService::validateCompanyIsClientMSP($company);
        DB::beginTransaction();

        try {
            foreach ($request->stack as $categoryId => $stacks) {
                collect($stacks)->map(function ($stack) use ($categoryId, $company, $loggedUser) {
                    // Creating of searching for product
                    $product = ClientProductService::getProduct(
                        $company,
                        $stack["product_id"] ?? null,
                        $stack["product_name"] ?? null,
                        $stack["category_id"] ?? null,
                        $stack["product_description"] ?? "",
                    );
                    $stackCompany = ClientVendorService::getVendor(
                        $company,
                        $stack["stack_company_id"] ?? null,
                        $stack["stack_company_name"] ?? null,
                        $stack["is_distributor"] ?? false,
                    );
                    MyStackCustomerService::validateDuplicateStack(
                        $product->id,
                        $stackCompany->id,
                        $categoryId
                    );
                    // Creating or restoring the stack item
                    MyStackCustomerService::createOrRestoreStack(
                        $company->id,
                        $stackCompany->id,
                        $product->id,
                        $categoryId,
                        $stack['partner_status'] ?? null,
                        $loggedUser->id,
                    );
                });
            }
            DB::commit();
        } catch (QueryException $error) {
            DB::rollBack();
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($error->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_CUSTOMER_STACK'),
                ]);
            }

            throw $error;
        }
        $query = $company->customerStack();
        $result = $query->wherePivotIn('category_id', array_keys($request->stack))->get();
        MyStackService::appendStackedCategories($result);

        return CustomerStackResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/customer/{company}/my-stack/update",
     *     operationId="customer/{company}/my-stack/update",
     *     tags={"MyStackCustomerController"},
     *     summary="Update an array of companies to the company stack",
     *     description="Update an array of companies to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/update
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
        "stack_company_id": "required|numeric|exists:companies,id",
        "category_id": "sometimes|required|numeric|exists:categories,id,is_hidden,0",
        "product_id": "sometimes|required|numeric|exists:products,id",
        "partner_status": "sometimes|required|string|allowed_values:MyStackPartnerStatus"
     }
     * */
    // Bearer token needed
    public function update(CustomerStackUpdateRequest $request, Company $company)
    {
        $stack = CustomerStack::find($request->id);
        if (!$stack) {
            abort(404, "Stack not found");
        }

        try {
            $data = $request->validated();
            // Creating of searching for product
            $product = ClientProductService::getProduct(
                $company,
                $data["product_id"] ?? null,
                $data["product_name"] ?? null,
                $data["category_id"] ?? null,
                $data["product_description"] ?? null,
            );
            $stackCompany = ClientVendorService::getVendor(
                $company,
                $data["stack_company_id"] ?? null,
                $data["stack_company_name"] ?? null,
                $data["is_distributor"] ?? false,
            );
            MyStackCustomerService::validateDuplicateStack(
                $product->id,
                $stackCompany->id,
                $data["category_id"],
                $stack->id
            );
            $stack->update([
                "product_id" => $product->id,
                "stack_company_id" => $stackCompany->id,
                "category_id" => $data["category_id"] ?? null,
                "partner_status" => $data["partner_status"] ?? null,
            ]);
        } catch (QueryException $error) {
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($error->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_STACK'),
                ]);
            }

            throw $error;
        }
        $result = $company->customerStack()->wherePivot('id', $request->id)->get();
        MyStackService::appendStackedCategories($result);

        return new CustomerStackResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/customer/{company}/my-stack/delete",
     *     operationId="customer/{company}/my-stack/delete",
     *     tags={"MyStackCustomerController"},
     *     summary="Delete a company from the company stack",
     *     description="Delete a company from the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/delete
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
     }
     * */
    // Bearer token needed
    public function delete(CustomerStackDeleteRequest $request, Company $company): JsonResponse
    {
        CustomerStack::where([
            "company_id" => $company->id,
            "id" => $request->id,
        ])->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Put(
     *     path="/api/v1/customer/{company}/my-stack/update/partner-status",
     *     operationId="customer/{company}/my-stack/updatePartnerStatus",
     *     tags={"MyStackCustomerController"},
     *     summary="Update partner status to the company stack",
     *     description="Update partner status to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: PUT
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/update/partner-status
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
        "partner_status": "required|string|allowed_values:MyStackPartnerStatus"
     }
     * */
    // Bearer token needed
    public function updatePartnerStatus(CustomerStackUpdatePartnerStatusRequest $request, Company $company): CustomerStackResource
    {
        CustomerStack::where('id', $request->id)->where('company_id', $company->id)
            ->update([
                'partner_status' => $request->partner_status,
            ]);

        $result = $company->customerStack()->wherePivot('id', $request->id)->get();
        MyStackService::appendStackedCategories($result);

        return new CustomerStackResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/customer/{company}/my-stack/generate-csv",
     *     operationId="/api/v1/customer/{company}/my-stack/generate-csv",
     *     tags={"MyStackCustomerController"},
     *     summary="Export Csv",
     *     description="Export Csv",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/generate-csv
    // Bearer token needed
    public function generateCSV(request $request, Company $company)
    {
        CompanyService::validateCompanyIsClientMSP($company);

        $activeCompanyId = PermissionsHelper::getCompanyId($request);
        $requestedByParent = $activeCompanyId != $company->id;

        $customerColumns = CSVColumnsEnum::exportCustomerStack;
        if ($requestedByParent) {
            $customerColumns[] = "Source";
        }
        $customerManaged = collect(MyStackCustomerService::getMyStackCSVData($company))
            ->map(function ($item) use ($requestedByParent) {
                if ($requestedByParent) {
                    $item[] = "Customer Managed";
                }

                return $item;
            });
        $fromStack = $requestedByParent
            ? collect(MyStackService::getMyStackCSVData($company))
                ->map(function ($item) {
                    $item[] = "Installed from your Stack";

                    return $item;
                })
            : [];

        $fileName = 'customer-stack-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse(
            $customerColumns,
            collect($fromStack)->merge($customerManaged)->toArray(),
            $fileName
        );
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/customer/{company}/my-stack/generate-pdf",
     *     operationId="customer/{company}/my-stack/generatePDF",
     *     tags={"MyStackCustomerController"},
     *     summary="Download mystack PDF",
     *     description="Download mystack PDF",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/customer/{company}/my-stack/generate-pdf
    // Bearer token needed
    public function generatePDF(Request $request, Company $company)
    {
        CompanyService::validateCompanyIsClientMSP($company);
        $activeCompanyId = PermissionsHelper::getCompanyId($request);
        if ($activeCompanyId == $company->id) {
            // Requested by customer
            $pdfView = MyStackCustomerService::getMyStackPDFData($company);
        } else {
            // Requested by parent
            $fromStackPdfView = MyStackService::getMyStackPDFData($company);
            $customerManagedPdfView = MyStackCustomerService::getMyStackPDFData($company);
            $pdfView = view('CustomerStack.combinedStackPDF', compact('fromStackPdfView', 'customerManagedPdfView'))->render();
        }

        $pdf = Pdf::loadHTML($pdfView)
            ->setPaper('a4', 'landscape')
            ->setOption('enable-javascript', true)
            ->setOption('javascript-delay', 2000);
        $reportName = $company->name . '.pdf';

        return $pdf->download($reportName);
    }
}
