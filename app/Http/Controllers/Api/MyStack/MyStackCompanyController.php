<?php

namespace App\Http\Controllers\Api\MyStack;

use App\Enums\CSV\CSVColumnsEnum;
use App\Enums\MyStackPartnerStatus;
use App\Enums\Partner\PartnerPortalInvitationStatus;
use App\Enums\StatusScope\StatusScopeEnum;
use App\Helpers\UtilityHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\MyStack\AvailableVendorsRequest;
use App\Http\Requests\MyStack\MyStackAddClientUsageRequest;
use App\Http\Requests\MyStack\MyStackDeleteCategoryRequest;
use App\Http\Requests\MyStack\MyStackDeleteRequest;
use App\Http\Requests\MyStack\MyStackShowAllRequest;
use App\Http\Requests\MyStack\MyStackStoreRequest;
use App\Http\Requests\MyStack\MyStackUpdatePartnerStatusRequest;
use App\Http\Requests\MyStack\MyStackUpdateRequest;
use App\Http\Resources\Company\CompanySimpleResource;
use App\Http\Resources\MyStack\AvailableVendorResource;
use App\Http\Resources\MyStack\GeneralMissingAdoptionStackResource;
use App\Http\Resources\MyStack\MyStackAffiliateResource;
use App\Http\Resources\MyStack\MyStackResource;
use App\Models\Category\Category;
use App\Models\Company\Company;
use App\Models\MSPFollowingPartner;
use App\Models\MyStack\MyStack;
use App\Services\AuthService;
use App\Services\Company\CompanyService;
use App\Services\ImageService;
use App\Services\MyStack\CompanyClientStackService;
use App\Services\MyStack\MyStackService;
use App\Services\ProductService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use stdClass;

class MyStackCompanyController extends Controller
{
    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/affiliate/my-stack",
     *     operationId="company/{COMPANY_ID}/affiliate/my-stack/showAll",
     *     tags={"MyStackCompanyController"},
     *     summary="Show all stack for a particular company",
     *     description="Show all stack for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/affiliate/my-stack
    /*{
    	"search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAllAffiliate(MyStackShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsAffiliate($company);
        $request->merge(['paged' => false]);
        $affiliateResult = $this->loadStackForCompany($company, $request);
        $parentResult = $this->loadStackForCompany($company->parentCompany, $request);
        $result = $this->mergeAffiliateAndParentStacks($affiliateResult, $parentResult);
        MyStackService::appendStackedCategories($result);
        MyStackService::appendStackedProducts($result);
        $result = $this->searchProductRating($result, $request);

        return MyStackAffiliateResource::collection($result);
    }

        // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/affiliate/my-stack/categories-filled",
     *     operationId="company/{COMPANY_ID}/affiliate/my-stack/showAllCategoriesFilled",
     *     tags={"MyStackCompanyController"},
     *     summary="Show all categories filled for a particular company",
     *     description="Show all categories filled for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/affiliate/my-stack/categories-filled
    /*{
    	"search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAllAffiliateCategoriesFilled(MyStackShowAllRequest $request, Company $company): JsonResponse
    {
        CompanyService::validateCompanyIsAffiliate($company);
        $request->merge(['paged' => false]);
        $affiliateResult = $this->loadStackForCompany($company, $request);
        $parentResult = $this->loadStackForCompany($company->parentCompany, $request);
        $result = $this->mergeAffiliateAndParentStacks($affiliateResult, $parentResult);
        $numOfCategoriesFilled = $result->pluck('pivot.category_id')->filter()->unique()->count();

        return response()->json([
            'num_of_categories_filled' => $numOfCategoriesFilled,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack",
     *     operationId="company/{COMPANY_ID}/my-stack/showAll",
     *     tags={"MyStackCompanyController"},
     *     summary="Show all stack for a particular company",
     *     description="Show all stack for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAll(MyStackShowAllRequest $request, Company $company): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsMSP($company);

        $result = $this->loadStackForCompany($company, $request);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        CompanyClientStackService::appendClientUsage($company, $pageResults);

        MyStackService::appendStackedCategories($pageResults);
        MyStackService::appendStackedProducts($pageResults);

        $pageResults = $this->searchProductRating($pageResults, $request);
        if ($result instanceof LengthAwarePaginator) {
            $result->setCollection($pageResults);
        } else {
            $result = $pageResults;
        }

        return MyStackResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/categories-filled",
     *     operationId="company/{COMPANY_ID}/my-stack/showAllCategoriesFilled",
     *     tags={"MyStackCompanyController"},
     *     summary="Show all categories filled for a particular company",
     *     description="Show all categories filled for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/categories-filled
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function showAllCategoriesFilled(MyStackShowAllRequest $request, Company $company): JsonResponse
    {
        CompanyService::validateCompanyIsMSP($company);

        $result = $this->loadStackForCompany($company, $request);
        if ($result instanceof LengthAwarePaginator) {
            $pageResults = $result->getCollection();
        } else {
            $pageResults = $result;
        }

        $numOfCategoriesFilled = $pageResults->pluck('pivot.category_id')->filter()->unique()->count();

        return response()->json([
            'num_of_categories_filled' => $numOfCategoriesFilled,
        ]);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/missing-adoption-stack",
     *     operationId="company/{COMPANY_ID}/my-stack/missingAdoptionStack",
     *     tags={"MyStackCompanyController"},
     *     summary="Show's missing adoption stack for a particular company",
     *     description="Show's missing adoption stack for a particular company",
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/missing-adoption-stack
    // Bearer token needed
    public function missingAdoptionStack(Company $company): GeneralMissingAdoptionStackResource
    {
        CompanyService::validateCompanyIsAffiliate($company);
        $result = $this->prepareMissingAdoptionStack($company);

        return new GeneralMissingAdoptionStackResource($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/store",
     *     operationId="company/{COMPANY_ID}/my-stack/store",
     *     tags={"MyStackCompanyController"},
     *     summary="Store an array of companies to the company stack",
     *     description="Store an array of companies to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/store
    // Bearer token needed
    public function store(MyStackStoreRequest $request, Company $company): AnonymousResourceCollection
    {
        $loggedUser = AuthService::getAuthUser();
        CompanyService::validateCompanyIsMSP($company);
        DB::beginTransaction();

        try {
            foreach ($request->stack as $categoryId => $stackCompaniesIds) {
                collect($stackCompaniesIds)->map(function ($stack) use ($categoryId, $company, $loggedUser) {
                    if (!empty($stack['product_id'])) {
                        ProductService::validateProductCompanyOwnership($stack['product_id'], $stack['company_id']);
                    }
                    $stackCompanyID = ($stack['sold_by_distributor'] ?? false) && !empty($stack['distributor_id'])
                        ? $stack['distributor_id']
                        : $stack['company_id'];
                    if (!empty($stack['is_recommended_stack']) && $stack['is_recommended_stack'] === true) {
                        $myStack = $this->createMyStackItem($company->id, $stackCompanyID, $loggedUser->id, $categoryId,
                            null, null, $stack['is_recommended_stack']);
                    }
                    if (!empty($stack['product_id'])) {
                        $myStack = $this->createMyStackItem($company->id, $stackCompanyID, $loggedUser->id, $categoryId,
                            $stack['product_id'], $stack['partner_status'] ?? null,
                            $stack['is_recommended_stack'] ?? false);
                    }
                    if (!empty($stack['partner_status'])
                        && $stack['partner_status'] === MyStackPartnerStatus::currentPartner) {
                        CompanyClientStackService::addClientStack($myStack, $loggedUser,
                            $stack['client_ids'] ?? []);
                    }
                });
            }
            DB::commit();
        } catch (QueryException $ex) {
            DB::rollBack();
            Log::error($ex->getMessage());
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_STACK'),
                ]);
            }

            throw $ex;
        }
        if ($request->has('is_recommended_stack') && $request->is_recommended_stack) {
            $query = $company->myStack();
        } else {
            $query = $company->recommendedMyStack();
        }
        $result = $query->wherePivotIn('category_id', array_keys($request->stack))->get();
        ImageService::appendCompanyAvatars($result);
        MyStackService::appendStackedCategories($result);
        MyStackService::appendStackedProducts($result);

        return MyStackResource::collection($result);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/update",
     *     operationId="company/{COMPANY_ID}/my-stack/update",
     *     tags={"MyStackCompanyController"},
     *     summary="Update an array of companies to the company stack",
     *     description="Update an array of companies to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/update
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
        "stack_company_id": "required|numeric|exists:companies,id",
        "category_id": "sometimes|required|numeric|exists:categories,id,is_hidden,0",
        "product_id": "sometimes|required|numeric|exists:products,id",
        "partner_status": "sometimes|required|string|allowed_values:MyStackPartnerStatus"
     }
     * */
    // Bearer token needed
    public function update(MyStackUpdateRequest $request, Company $company)
    {
        try {
            $company->myStack()
                ->newPivotStatement()
                ->where('id', $request->id)
                ->update($request->validated());
        } catch (QueryException $ex) {
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.COMPANY_WITH_CATEGORY_AND_PRODUCT_ALREADY_ADDED_TO_STACK'),
                ]);
            }

            throw $ex;
        }
        $result = $company->myStack()->wherePivot('id', $request->id)->get();
        ImageService::appendCompanyAvatars($result);
        MyStackService::appendStackedCategories($result);
        MyStackService::appendStackedProducts($result);

        return new MyStackResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/delete",
     *     operationId="company/{COMPANY_ID}/my-stack/delete",
     *     tags={"MyStackCompanyController"},
     *     summary="Delete an array of companies to the company stack",
     *     description="Delete an array of companies to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/delete
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
     }
     * */
    // Bearer token needed
    public function delete(MyStackDeleteRequest $request, Company $company): JsonResponse
    {
        $myStack = MyStack::where('id', $request->id)->first();
        $myStack->clientStack()->forceDelete();
        $myStack->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Delete(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/delete-category",
     *     operationId="company/{COMPANY_ID}/my-stack/deleteCategory",
     *     tags={"MyStackCompanyController"},
     *     summary="Delete all my stack for selected category",
     *     description="Delete all my stack for selected category",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/delete-category
    /*
     {
        "category_id": "required|numeric|exists:my_stack,category_id",
     }
     * */
    // Bearer token needed
    public function deleteCategory(MyStackDeleteCategoryRequest $request, Company $company): JsonResponse
    {
        $query = MyStack::where('company_id', $company->id);
        $category = Category::with('subCategories')->find($request->category_id);
        if ($category->parent_id === null) {
            $query->whereIn('category_id', $category->subCategories->pluck('id'));
        } else {
            $query->where('category_id', $request->category_id);
        }
        $query->whereNull('deleted_at')->delete();

        return response()->json();
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/my-stack/available-vendors",
     *     operationId="company/availableVendors",
     *     tags={"MyStackCompanyController"},
     *     summary="Get All vendors",
     *     description="Returns all vendors",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/my-stack/available-vendors
    // BODY
    /*{
    	"paged": "sometimes|required|boolean",
    	"page": "sometimes|required|numeric",
    	"items_per_page": "sometimes|required|numeric",
        "search_word": "sometimes|string|min:3",
    	"product_categories_ids": "sometimes|numericArray|min:1|exists:categories,id",
    	"partner_flag": "sometimes|required|boolean",
    	"rating": "sometimes|required|numeric"
    	"partner_status": "sometimes|required|allowedValues"
    }*/
    // Bearer token needed
    public function availableVendors(AvailableVendorsRequest $request, Company $company): AnonymousResourceCollection
    {
        $company->load('partnerIFollow');
        CompanyService::validateCompanyIsMSP($company);

        $query = $this->buildAvailableVendorsQuery($company, $request);
        $result = UtilityHelper::getSearchRequestQueryResults($request, $query);

        if ($result instanceof LengthAwarePaginator) {
            $vendors = $result->getCollection();

            $vendors = $this->applyCompanyRating($vendors, $request);
            ImageService::appendCompanyAvatars($vendors);

            $vendors = $vendors->transform(fn ($vendor) => $this->appendPartnerStatus($vendor, $company));

            $result->setCollection($vendors);
        } else {
            $result = $this->applyCompanyRating($result, $request);
            ImageService::appendCompanyAvatars($result);

            $result = $result->transform(fn ($vendor) => $this->appendPartnerStatus($vendor, $company));
        }

        return AvailableVendorResource::collection($result);
    }

    private function buildAvailableVendorsQuery(Company $company, AvailableVendorsRequest $request): EloquentBuilder
    {
        $query = Company::select([
            'id', 'name', 'friendly_url', 'subdomain',
            'partner_flag', 'is_distributor', 'manage_clients',
        ])
            ->with(['enumType:id,type_is_of_vendor', 'companyClaimers:id'])
            ->where('id', '<>', config('custom.channel_program_company.id'))
            ->whereHas('companyClaimers')
            ->whereHas('enumType', fn ($q) => $q->where('type_is_of_vendor', true));

        if ($request->filled('partner_flag')) {
            $query->where('partner_flag', $request->partner_flag === 'available');
        }

        $this->searchPartnerStatus($company, $query, $request);

        if ($request->filled('product_categories_ids')) {
            $query->whereHas('products.categories', function ($q) use ($request) {
                $q->whereIn('categories.id', $request->product_categories_ids)
                    ->orWhereIn('categories.parent_id', $request->product_categories_ids);
            });
        }

        if ($request->filled('search_word')) {
            $query->whereRaw('LOWER(companies.name) LIKE ?', ['%' . Str::lower($request->search_word) . '%']);
        }

        return $query->orderByRaw('UPPER(CAST(name AS TEXT))');
    }

    private function applyCompanyRating(Collection $vendors, AvailableVendorsRequest $request): Collection
    {
        return $this->searchCompanyRating($vendors, $request);
    }

    private function appendPartnerStatus($vendor, Company $company)
    {
        $vendor->partner_status = $company->partnerIFollow
            ->where('pivot.followed_partner_id', $vendor->id)
            ->first()?->pivot->status;

        return $vendor;
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Post(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/update/partner-status",
     *     operationId="company/{COMPANY_ID}/my-stack/updatePartnerStatus",
     *     tags={"MyStackCompanyController"},
     *     summary="Update partner status to the company stack",
     *     description="Update partner status to the company stack",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/update/partner-status
    /*
     {
        "id": "required|numeric|exists:my_stack,id,company_id,COMPANY_ID",
        "partner_status": "required|string|allowed_values:MyStackPartnerStatus"
     }
     * */
    // Bearer token needed
    public function updatePartnerStatus(MyStackUpdatePartnerStatusRequest $request, Company $company): MyStackResource
    {
        MyStack::where('id', $request->id)->where('company_id', $company->id)
            ->update([
                'partner_status' => $request->partner_status,
            ]);

        $result = $company->myStack()->wherePivot('id', $request->id)->get();
        ImageService::appendCompanyAvatars($result);
        MyStackService::appendStackedCategories($result);
        MyStackService::appendStackedProducts($result);

        return new MyStackResource($result->first());
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{company}/my-stack/generate-csv",
     *     operationId="/api/v1/company/{company}/my-stack/generate-csv",
     *     tags={"MyStackCompanyController"},
     *     summary="Export Csv",
     *     description="Export Csv",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity"
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/my-stack/generate-csv
    // Bearer token needed
    public function generateCSV(Company $company)
    {
        $columns = CSVColumnsEnum::exportMyStack;
        $data = MyStackService::getMyStackCSVData($company);

        $fileName = 'my-stack-' . time() . '.csv';

        return UtilityHelper::getCSVFileResponse($columns, $data, $fileName);
    }

    // <editor-fold desc="oa comments">
    /**
     * @OA\Get(
     *     path="/api/v1/company/{COMPANY_ID}/my-stack/download-pdf",
     *     operationId="company/{COMPANY_ID}/my-stack/downloadPDF",
     *     tags={"MyStackCompanyController"},
     *     summary="Download mystack PDF",
     *     description="Download mystack PDF",
     *     security={{"bearerAuth": {}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden"
     *     )
     * )
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{COMPANY_ID}/my-stack/download-pdf
    // Bearer token needed
    public function downloadPDF(Company $company)
    {
        CompanyService::validateCompanyIsMSP($company);
        $reportName = $company->name . '.pdf';
        $pdfView = MyStackService::getMyStackPDFData($company);
        $pdf = Pdf::loadHTML($pdfView)
            ->setPaper('a4', 'landscape')
            ->setOption('enable-javascript', true)
            ->setOption('javascript-delay', 2000);

        return $pdf->download($reportName);
    }

    // <editor-fold desc="oa comments">
    /**
     * Show clients using a specific stack.
     *
     * @OA\Get(
     *     path="company/{company}/my-stack/{myStack}/client-usage",
     *     operationId="showClientsUsingStack",
     *     tags={"MyStackCompanyController"},
     *     summary="Show clients using a stack",
     *     description="Retrieves all clients using a specific stack.",
     *
     *     @OA\Parameter(
     *         name="company",
     *         in="path",
     *         description="ID of the company",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Parameter(
     *         name="myStack",
     *         in="path",
     *         description="ID of the MyStack",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="List of clients using the stack.",
     *
     *         @OA\JsonContent(
     *             type="array",
     *
     *             @OA\Items(ref="CompanySimpleResource")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - The request was valid, but the server is refusing action."
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found - The requested resource could not be found."
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: GET
    // http://127.0.0.1:8000/api/v1/company/{company}/my-stack/{myStack}/client-usage
    // Bearer token needed
    public function showClientsUsingStack(Company $company, MyStack $myStack): AnonymousResourceCollection
    {
        CompanyService::validateCompanyIsMSP($company);
        $response = $myStack->clientsUsing()->get();
        ImageService::appendCompanyAvatars($response);

        return CompanySimpleResource::collection($response);
    }

    // <editor-fold desc="oa comments">
    /**
     * Add usage relationship of a specific stack for the given list of clients.
     *
     * @OA\Post(
     *     path="company/{company}/my-stack/{myStack}/client-usage",
     *     operationId="addClientUsage",
     *     tags={"MyStackCompanyController"},
     *     summary="Add usage relationship of a stack for clients",
     *     description="Adds the usage relationship of a specific stack for the provided list of clients.",
     *
     *     @OA\Parameter(
     *         name="company",
     *         in="path",
     *         description="ID of the company",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Parameter(
     *         name="myStack",
     *         in="path",
     *         description="ID of the MyStack",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *         description="Client IDs",
     *
     *         @OA\JsonContent(
     *             required={"clients_ids"},
     *
     *             @OA\Property(property="clients_ids", type="array", @OA\Items(type="integer"))
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Client usage relationship successfully added."
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - The request was valid, but the server is refusing action."
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found - The requested resource could not be found."
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Unprocessable Entity - The request was well-formed but was unable to be followed due to semantic errors."
     *     )
     * )
     */
    // </editor-fold>
    // API Call: POST
    // http://127.0.0.1:8000/api/v1/company/{company}/my-stack/{myStack}/client-usage
    /*
     {
            'clients_ids': 'required|array|min:1',
            'clients_ids.*': 'required|numeric|exists:companies,id',
     }
     */
    // Bearer token needed
    public function addClientUsage(MyStackAddClientUsageRequest $request, Company $company, MyStack $myStack)
    {
        $loggedUser = AuthService::getAuthUser();
        CompanyService::validateCompanyIsMSP($company);

        try {
            return CompanyClientStackService::addClientStack($myStack, $loggedUser, $request->clients_ids);
        } catch (QueryException $ex) {
            $searchForMessage = 'duplicate key value violates unique constraint';
            if (Str::contains($ex->getMessage(), $searchForMessage)) {
                throw ValidationException::withMessages([
                    config('genericMessages.error.CLIENT_ALREADY_ASSIGNED_TO_STACK'),
                ]);
            }

            throw $ex;
        }
    }

    // <editor-fold desc="oa comments">
    /**
     * Remove usage relationship of a specific stack for the given list of clients.
     *
     * @OA\Delete(
     *     path="company/{company}/my-stack/{myStack}/client-usage",
     *     operationId="deleteClientUsage",
     *     tags={"MyStackCompanyController"},
     *     summary="Remove usage relationship of a stack for clients",
     *     description="Removes the usage relationship of a specific stack for the provided list of clients.",
     *
     *     @OA\Parameter(
     *         name="company",
     *         in="path",
     *         description="ID of the company",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Parameter(
     *         name="myStack",
     *         in="path",
     *         description="ID of the MyStack",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *         description="Client IDs",
     *
     *         @OA\JsonContent(
     *             required={"clients_ids"},
     *
     *             @OA\Property(property="clients_ids", type="array", @OA\Items(type="integer"))
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Client usage relationship successfully removed."
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - The request was valid, but the server is refusing action."
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Not Found - The requested resource could not be found."
     *     )
     * )
     *
     * @throws ValidationException
     */
    // </editor-fold>
    // API Call: DELETE
    // http://127.0.0.1:8000/api/v1/company/{company}/my-stack/{myStack}/client-usage
    /*
     {
            'clients_ids': 'required|array|min:1',
            'clients_ids.*': 'required|numeric|exists:companies,id',
     }
     */
    // Bearer token needed
    public function deleteClientUsage(MyStackAddClientUsageRequest $request, Company $company, MyStack $myStack)
    {
        $loggedUser = AuthService::getAuthUser();
        CompanyService::validateCompanyIsMSP($company);

        return CompanyClientStackService::deleteClientStack($myStack, $loggedUser, $request->clients_ids);
    }

    /**
     * @throws ValidationException
     */
    private function loadStackForCompany(
        Company $company, MyStackShowAllRequest $request): mixed
    {
        if ($request->has('is_recommended_stack') && $request->is_recommended_stack) {
            $query = $company->recommendedMyStack();
        } else {
            $query = $company->myStack();
        }
        $query->addSelect([
            'prm_partner_status' => MSPFollowingPartner::select('company_partners.status')
                ->whereColumn('my_stack.stack_company_id', 'company_partners.followed_partner_id')
                ->whereColumn('my_stack.company_id', 'company_partners.follower_partner_id')
                ->whereNull('deleted_at')
                ->limit(1),
        ])->with(['statusRelation']);

        $query = $this->prepareShowAllQuery($query, $request);

        $this->searchPartnerStatus($company, $query, $request);
        if ($request->has('partner_flag')) {
            $partnerFlag = false;
            if ($request->partner_flag === 'available') {
                $partnerFlag = true;
            }
            $search['partner_flag'] = $partnerFlag;
            $request['search'] = $search;
        }
        $query = UtilityHelper::prepareSearchQuery($query, $request->search, new MyStack());

        return UtilityHelper::getSearchRequestQueryResults($request, $query);
    }

    /**
     * Calculates the status of each element from the affiliate stack
     * against its parent stack. The corresponding statuses are:
     * added_to_stack (both have it)
     * not_on_stack (parent has it but affiliate does not)
     * affiliate_choice (affiliate has it but parent does not)
     */
    private function mergeAffiliateAndParentStacks(Collection $affiliateResult, Collection $parentResult): Collection
    {
        $finalResult = new Collection();
        foreach ($parentResult as $parent) {
            $parent->stack_status = 'not_on_stack';
            $finalResult->push($parent);
        }
        foreach ($affiliateResult as $affiliate) {
            $found = false;
            foreach ($parentResult as $parent) {
                if ($parent->pivot->stack_company_id === $affiliate->pivot->stack_company_id
                    && $parent->pivot->category_id === $affiliate->pivot->category_id
                    && $parent->pivot->product_id === $affiliate->pivot->product_id) {
                    $found = true;
                    $finalResult = $finalResult->filter(function ($item) use ($parent) {
                        if ($parent->pivot->id !== $item->pivot->id) {
                            return $item;
                        }

                        return null;
                    });
                }
            }
            $affiliate->stack_status = $found ? 'added_to_stack' : 'affiliate_choice';
            $finalResult->push($affiliate);
        }

        return $finalResult;
    }

    private function searchPartnerStatus($company, $query, $request)
    {
        if ($request->has('partner_status')) {
            if (!is_array($request->partner_status)) {
                $request->merge(['partner_status' => [$request->partner_status]]);
            }
            $partnerStatuses = PartnerPortalInvitationStatus::getValues($request->partner_status);
            $query->where(function ($query) use ($company, $request, $partnerStatuses) {
                if (in_array(PartnerPortalInvitationStatus::NotRequested, $partnerStatuses)) {
                    $query->whereNotIn('companies.id', $company->partnerIFollow?->pluck('pivot.followed_partner_id') ?? [])
                        ->where('partner_flag', true);
                }
                if (array_intersect($partnerStatuses, [
                    PartnerPortalInvitationStatus::Accepted,
                    PartnerPortalInvitationStatus::Invited,
                    PartnerPortalInvitationStatus::Requested,
                ])) {
                    $query->orWhereIn('companies.id', $company->partnerIFollow->filter(function ($partner) use ($request) {
                        return in_array($partner->pivot->status, PartnerPortalInvitationStatus::getValues($request->partner_status));
                    })?->pluck('pivot.followed_partner_id') ?? []);
                }
            });
        }

        return $query;
    }

    private function searchCompanyRating($companies, $request)
    {
        CompanyService::calculateRatings($companies);
        if ($request->has('rating')) {
            $companies = $companies->filter(function ($vendor) use ($request) {
                return $vendor->rating !== null && $vendor->rating > $request->rating;
            });
        }

        return $companies;
    }

    private function searchProductRating($companies, $request)
    {
        if ($request->has('rating') && $request->rating) {
            $products = $companies->pluck('product')->filter(function ($product) use ($request) {
                return $product->rating !== null && $product->rating >= $request->rating;
            });
            $companies = $companies->filter(function ($company) use ($products) {
                return $products->contains('id', $company->product->id);
            });
        }

        return $companies;
    }

    private function prepareShowAllQuery($query, $request)
    {
        $query
            ->join('products', 'my_stack.product_id', '=', 'products.id')
            ->join('categories as sub_categories', 'my_stack.category_id', '=', 'sub_categories.id')
            ->join('categories as parent_categories', 'sub_categories.parent_id', '=', 'parent_categories.id')
            ->when($request->has('is_recommended_stack') && $request->is_recommended_stack, function ($query) use ($request) {
                $query->where('is_recommended_stack', $request->is_recommended_stack);
            })
            ->when($request->has('search_word'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    $query->whereRaw("(lower(companies.name) LIKE '%" . Str::lower($request->search_word) . "%')")
                        ->orWhereRaw("(lower(parent_categories.name) LIKE '%" . Str::lower($request->search_word) . "%')")
                        ->orWhereRaw("(lower(sub_categories.name) LIKE '%" . Str::lower($request->search_word) . "%')")
                        ->orWhereRaw("(lower(products.name) LIKE '%" . Str::lower($request->search_word) . "%')");
                });
            })->when($request->has('category'), function ($query) use ($request) {
                $query->whereIn('sub_categories.parent_id', $request->category);
            })->when($request->has('sub_category'), function ($query) use ($request) {
                $query->whereIn('my_stack.category_id', $request->sub_category);
            })
            ->when(!$request->has('order_by'), function ($query) {
                $query->orderByRaw(
                    "
            CASE WHEN LOWER(parent_categories.name) ~ '^[a-zA-Z]' THEN 0 ELSE 1 END, LOWER(parent_categories.name) ASC,
            CASE WHEN LOWER(sub_categories.name) ~ '^[a-zA-Z]' THEN 0 ELSE 1 END,LOWER(sub_categories.name) ASC,
            CASE WHEN LOWER(products.name) ~ '^[a-zA-Z]' THEN 0 ELSE 1 END, LOWER(products.name) ASC"
                );
            })
            ->where(function ($q) {
                $q->whereHas('companyClaimers')
                    ->orWhereHas('statusRelation', function ($q) {
                        $q->whereIn('status_scope.status', [StatusScopeEnum::pending, StatusScopeEnum::declined]);
                    });
            })
            ->whereHas('enumType', fn ($q) => $q->where('type_is_of_vendor', true));

        return $query;
    }

    private function prepareMissingAdoptionStack(Company $company): stdClass
    {
        $recommendedStack = $company->parentCompany->recommendedMyStack;
        $pivots = $recommendedStack->pluck('pivot');
        $recommendedMyStackFindings = CompanyService::findBrandStackAdoptions($company, $pivots);
        $missingAdoptionStacks = $recommendedMyStackFindings['missing_categories']
            ->merge($recommendedMyStackFindings['missing_products']);
        $results = new stdClass();
        $results->items = $missingAdoptionStacks->groupBy('category_id')
            ->map(function ($missingAdoptionStack) {
                $result = new stdClass();
                // here we need to check how to get the products from the company and not from the $missingAdoptionStack
                $result->products = $missingAdoptionStack
                    ->filter(fn ($item) => $item->product)
                    ->map(fn ($item) => $item->product->load(['categories.parentCategory:id,parent_id,name',
                        'company.avatar:id,model_type,model_id,uuid,collection_name,name,file_name,mime_type,disk,conversions_disk,size,custom_properties', ]));
                $result->sub_category = $missingAdoptionStack->pluck('category')
                    ->first()->load(['parentCategory:id,parent_id,name']);

                return $result;
            })->flatten();
        $results->missing_categories_count = $recommendedMyStackFindings['missing_categories']->count();
        $results->missing_products_count = $recommendedMyStackFindings['missing_products']->count();

        return $results;
    }

    private function createMyStackItem(string $companyId, string $stackCompanyID, string $loggedUserId,
        string $categoryId, ?string $productId = null,
        ?string $partnerStatus = null, bool $isRecommendedStack = false): MyStack
    {
        $myStack = MyStackService::createOrRestoreStack(
            $companyId,
            $stackCompanyID,
            $productId,
            $categoryId,
            $partnerStatus,
            $loggedUserId,
            $isRecommendedStack
        );

        return $myStack;
    }
}
